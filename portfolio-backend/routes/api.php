<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ProjectController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Public API routes
Route::prefix('v1')->group(function () {
    // Projects
    Route::apiResource('projects', ProjectController::class);
    Route::get('projects-categories', [ProjectController::class, 'categories']);
    
    // Contact form endpoint
    Route::post('contact', function (Request $request) {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000'
        ]);

        // Here you would typically send an email or store in database
        // For now, we'll just return success
        
        return response()->json([
            'success' => true,
            'message' => 'Message sent successfully'
        ]);
    });
    
    // Portfolio stats
    Route::get('stats', function () {
        return response()->json([
            'success' => true,
            'data' => [
                'projects_completed' => '50+',
                'years_experience' => '3+',
                'client_satisfaction' => '100%',
                'technologies_used' => '20+'
            ]
        ]);
    });
});
