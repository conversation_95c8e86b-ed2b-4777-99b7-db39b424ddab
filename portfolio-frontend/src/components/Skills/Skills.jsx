import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useIntersectionObserver } from 'react-intersection-observer';
import styled from 'styled-components';

const SkillsSection = styled.section`
  min-height: 100vh;
  padding: 5rem 2rem;
  background: #0a0a0a;
  position: relative;
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const SectionTitle = styled(motion.h2)`
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  background: linear-gradient(135deg, #ffffff 0%, #00ff88 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: ${props => props.theme.fonts.secondary};
`;

const SkillsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
`;

const SkillCategory = styled(motion.div)`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #00ff88, #00cc6a);
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }

  &:hover::before {
    transform: scaleX(1);
  }
`;

const CategoryTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 600;
  color: ${props => props.theme.colors.accent};
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const CategoryIcon = styled.span`
  font-size: 1.8rem;
`;

const SkillsList = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
`;

const SkillTag = styled(motion.div)`
  background: rgba(0, 255, 136, 0.1);
  border: 1px solid rgba(0, 255, 136, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  color: ${props => props.theme.colors.text};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 255, 136, 0.2);
    border-color: ${props => props.theme.colors.accent};
    transform: translateY(-2px);
  }
`;

const InteractiveDemo = styled.div`
  margin-top: 4rem;
  text-align: center;
`;

const DemoTitle = styled.h3`
  font-size: 2rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin-bottom: 2rem;
`;

const CodeBlock = styled(motion.div)`
  background: #1a1a1a;
  border-radius: 15px;
  padding: 2rem;
  margin: 2rem auto;
  max-width: 600px;
  text-align: left;
  border: 1px solid rgba(0, 255, 136, 0.3);
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.9rem;
  line-height: 1.6;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
  }

  &:hover::before {
    transform: translateX(100%);
  }
`;

const CodeLine = styled(motion.div)`
  margin-bottom: 0.5rem;
  
  .keyword { color: #ff6b6b; }
  .string { color: #4ecdc4; }
  .function { color: #45b7d1; }
  .comment { color: #6c757d; }
  .variable { color: #00ff88; }
`;

const Skills = () => {
  const [selectedSkill, setSelectedSkill] = useState(null);
  
  const [titleRef, titleInView] = useIntersectionObserver({
    threshold: 0.3,
    triggerOnce: true,
  });

  const [skillsRef, skillsInView] = useIntersectionObserver({
    threshold: 0.2,
    triggerOnce: true,
  });

  const skillCategories = [
    {
      title: "Frontend Development",
      icon: "🎨",
      skills: ["React", "Vue.js", "JavaScript", "TypeScript", "HTML5", "CSS3", "Sass", "Tailwind CSS", "Framer Motion", "Three.js"]
    },
    {
      title: "Backend Development",
      icon: "⚙️",
      skills: ["Laravel", "Node.js", "PHP", "Python", "Express.js", "MySQL", "PostgreSQL", "MongoDB", "Redis", "REST APIs"]
    },
    {
      title: "Tools & Technologies",
      icon: "🛠️",
      skills: ["Git", "Docker", "AWS", "Webpack", "Vite", "Jest", "Cypress", "Figma", "Adobe Creative Suite", "Linux"]
    },
    {
      title: "3D & Animation",
      icon: "🎭",
      skills: ["Three.js", "WebGL", "GSAP", "Lottie", "Blender", "After Effects", "Canvas API", "SVG Animation"]
    }
  ];

  const codeExample = [
    { text: "// Interactive 3D Portfolio Component", class: "comment" },
    { text: "const ", class: "keyword" },
    { text: "Portfolio", class: "function" },
    { text: " = () => {", class: "keyword" },
    { text: "  const [", class: "keyword" },
    { text: "isAnimating", class: "variable" },
    { text: ", ", class: "keyword" },
    { text: "setIsAnimating", class: "function" },
    { text: "] = ", class: "keyword" },
    { text: "useState", class: "function" },
    { text: "(", class: "keyword" },
    { text: "false", class: "string" },
    { text: ");", class: "keyword" },
    { text: "", class: "" },
    { text: "  return (", class: "keyword" },
    { text: "    <", class: "keyword" },
    { text: "Canvas", class: "function" },
    { text: ">", class: "keyword" },
    { text: "      <", class: "keyword" },
    { text: "AnimatedMesh", class: "function" },
    { text: " />", class: "keyword" },
    { text: "    </", class: "keyword" },
    { text: "Canvas", class: "function" },
    { text: ">", class: "keyword" },
    { text: "  );", class: "keyword" },
    { text: "};", class: "keyword" },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <SkillsSection id="skills">
      <Container>
        <SectionTitle
          ref={titleRef}
          initial={{ y: 50, opacity: 0 }}
          animate={titleInView ? { y: 0, opacity: 1 } : { y: 50, opacity: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          Skills & Expertise
        </SectionTitle>

        <SkillsGrid
          ref={skillsRef}
          as={motion.div}
          variants={containerVariants}
          initial="hidden"
          animate={skillsInView ? "visible" : "hidden"}
        >
          {skillCategories.map((category, index) => (
            <SkillCategory
              key={index}
              variants={itemVariants}
              whileHover={{ y: -10, scale: 1.02 }}
              transition={{ duration: 0.3 }}
            >
              <CategoryTitle>
                <CategoryIcon>{category.icon}</CategoryIcon>
                {category.title}
              </CategoryTitle>
              
              <SkillsList>
                {category.skills.map((skill, skillIndex) => (
                  <SkillTag
                    key={skillIndex}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setSelectedSkill(skill)}
                    animate={selectedSkill === skill ? { 
                      background: "rgba(0, 255, 136, 0.3)",
                      scale: 1.05 
                    } : {}}
                  >
                    {skill}
                  </SkillTag>
                ))}
              </SkillsList>
            </SkillCategory>
          ))}
        </SkillsGrid>

        <InteractiveDemo>
          <DemoTitle>Interactive Code Preview</DemoTitle>
          <CodeBlock
            initial={{ opacity: 0, y: 30 }}
            animate={skillsInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            {codeExample.map((line, index) => (
              <CodeLine
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={skillsInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
                transition={{ duration: 0.5, delay: 0.7 + index * 0.1 }}
              >
                <span className={line.class}>{line.text}</span>
              </CodeLine>
            ))}
          </CodeBlock>
        </InteractiveDemo>
      </Container>
    </SkillsSection>
  );
};

export default Skills;
