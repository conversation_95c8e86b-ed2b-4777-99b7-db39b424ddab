import React, { useRef } from 'react';
import { motion, useScroll, useTransform, useInView } from 'framer-motion';
import { useIntersectionObserver } from 'react-intersection-observer';
import styled from 'styled-components';

const AboutSection = styled.section`
  min-height: 100vh;
  padding: 5rem 2rem;
  background: linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
  position: relative;
  overflow: hidden;
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
`;

const SectionTitle = styled(motion.h2)`
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  background: linear-gradient(135deg, #ffffff 0%, #00ff88 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: ${props => props.theme.fonts.secondary};
`;

const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
`;

const TextContent = styled(motion.div)`
  font-size: 1.1rem;
  line-height: 1.8;
  color: ${props => props.theme.colors.textSecondary};
`;

const Paragraph = styled(motion.p)`
  margin-bottom: 1.5rem;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const Highlight = styled.span`
  color: ${props => props.theme.colors.accent};
  font-weight: 600;
`;

const ImageContainer = styled(motion.div)`
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
  padding: 3px;
`;

const ImageWrapper = styled.div`
  background: #1a1a1a;
  border-radius: 17px;
  padding: 2rem;
  text-align: center;
`;

const ProfileImage = styled(motion.div)`
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 0 auto 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
  color: white;
  font-weight: bold;
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 4rem;

  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
`;

const StatCard = styled(motion.div)`
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
`;

const StatNumber = styled(motion.div)`
  font-size: 2.5rem;
  font-weight: 700;
  color: ${props => props.theme.colors.accent};
  margin-bottom: 0.5rem;
  font-family: ${props => props.theme.fonts.secondary};
`;

const StatLabel = styled.div`
  color: ${props => props.theme.colors.textSecondary};
  font-size: 1rem;
`;

const FloatingElements = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
`;

const FloatingShape = styled(motion.div)`
  position: absolute;
  width: ${props => props.size}px;
  height: ${props => props.size}px;
  background: linear-gradient(135deg, rgba(0, 255, 136, 0.1) 0%, rgba(0, 204, 106, 0.1) 100%);
  border-radius: 50%;
  filter: blur(1px);
`;

const About = () => {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], ["100px", "-100px"]);
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);

  const [titleRef, titleInView] = useIntersectionObserver({
    threshold: 0.3,
    triggerOnce: true,
  });

  const [contentRef, contentInView] = useIntersectionObserver({
    threshold: 0.2,
    triggerOnce: true,
  });

  const [statsRef, statsInView] = useIntersectionObserver({
    threshold: 0.3,
    triggerOnce: true,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  const stats = [
    { number: "50+", label: "Projects Completed" },
    { number: "3+", label: "Years Experience" },
    { number: "100%", label: "Client Satisfaction" },
  ];

  return (
    <AboutSection ref={ref} id="about">
      <FloatingElements>
        {[...Array(6)].map((_, i) => (
          <FloatingShape
            key={i}
            size={Math.random() * 100 + 50}
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              y,
            }}
            animate={{
              y: [0, -30, 0],
              x: [0, Math.random() * 20 - 10, 0],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        ))}
      </FloatingElements>

      <Container>
        <SectionTitle
          ref={titleRef}
          initial={{ y: 50, opacity: 0 }}
          animate={titleInView ? { y: 0, opacity: 1 } : { y: 50, opacity: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          About Me
        </SectionTitle>

        <ContentGrid ref={contentRef}>
          <TextContent
            variants={containerVariants}
            initial="hidden"
            animate={contentInView ? "visible" : "hidden"}
          >
            <Paragraph variants={itemVariants}>
              Hello! I'm <Highlight>DVS CodeCraft</Highlight>, a passionate full-stack developer 
              with a love for creating <Highlight>immersive digital experiences</Highlight>. 
              My journey in web development started 3 years ago, and I've been constantly 
              evolving my skills to stay at the forefront of technology.
            </Paragraph>
            
            <Paragraph variants={itemVariants}>
              I specialize in <Highlight>modern web technologies</Highlight> including React, 
              Laravel, Node.js, and Three.js. My expertise extends to creating 
              <Highlight> interactive animations</Highlight>, 3D visualizations, and 
              responsive web applications that not only look stunning but also provide 
              exceptional user experiences.
            </Paragraph>
            
            <Paragraph variants={itemVariants}>
              When I'm not coding, you can find me exploring new technologies, 
              contributing to open-source projects, or experimenting with 
              <Highlight> creative coding</Highlight> and digital art. I believe in 
              continuous learning and pushing the boundaries of what's possible on the web.
            </Paragraph>
          </TextContent>

          <ImageContainer
            initial={{ scale: 0.8, opacity: 0 }}
            animate={contentInView ? { scale: 1, opacity: 1 } : { scale: 0.8, opacity: 0 }}
            transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
          >
            <ImageWrapper>
              <ProfileImage
                whileHover={{ scale: 1.05, rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                DVS
              </ProfileImage>
              <motion.p
                style={{ color: '#a0a0a0', fontSize: '1rem' }}
                initial={{ opacity: 0 }}
                animate={contentInView ? { opacity: 1 } : { opacity: 0 }}
                transition={{ delay: 0.8 }}
              >
                Designed & Developed by DVS Codecraft
              </motion.p>
            </ImageWrapper>
          </ImageContainer>
        </ContentGrid>

        <StatsContainer ref={statsRef}>
          {stats.map((stat, index) => (
            <StatCard
              key={index}
              initial={{ y: 50, opacity: 0 }}
              animate={statsInView ? { y: 0, opacity: 1 } : { y: 50, opacity: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              whileHover={{ y: -10, scale: 1.05 }}
            >
              <StatNumber
                initial={{ scale: 0 }}
                animate={statsInView ? { scale: 1 } : { scale: 0 }}
                transition={{ duration: 0.5, delay: index * 0.2 + 0.3 }}
              >
                {stat.number}
              </StatNumber>
              <StatLabel>{stat.label}</StatLabel>
            </StatCard>
          ))}
        </StatsContainer>
      </Container>
    </AboutSection>
  );
};

export default About;
