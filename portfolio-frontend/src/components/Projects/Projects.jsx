import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useIntersectionObserver } from 'react-intersection-observer';
import { Canvas, useFrame } from '@react-three/fiber';
import { Box, Sphere } from '@react-three/drei';
import styled from 'styled-components';

const ProjectsSection = styled.section`
  min-height: 100vh;
  padding: 5rem 2rem;
  background: linear-gradient(180deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
  position: relative;
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const SectionTitle = styled(motion.h2)`
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  background: linear-gradient(135deg, #ffffff 0%, #00ff88 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: ${props => props.theme.fonts.secondary};
`;

const FilterButtons = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
`;

const FilterButton = styled(motion.button)`
  background: ${props => props.active ? 'linear-gradient(135deg, #00ff88, #00cc6a)' : 'transparent'};
  border: 2px solid ${props => props.theme.colors.accent};
  color: ${props => props.active ? '#000' : props.theme.colors.accent};
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: ${props => props.theme.colors.accent};
    color: #000;
    transform: translateY(-2px);
  }
`;

const ProjectsGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
`;

const ProjectCard = styled(motion.div)`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  cursor: pointer;
  position: relative;
  height: 400px;
`;

const ProjectImage = styled.div`
  height: 200px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
`;

const CanvasContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
`;

const ProjectContent = styled.div`
  padding: 1.5rem;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
`;

const ProjectTitle = styled.h3`
  font-size: 1.3rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin-bottom: 0.5rem;
`;

const ProjectDescription = styled.p`
  color: ${props => props.theme.colors.textSecondary};
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 1rem;
`;

const ProjectTech = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
`;

const TechTag = styled.span`
  background: rgba(0, 255, 136, 0.1);
  border: 1px solid rgba(0, 255, 136, 0.3);
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  color: ${props => props.theme.colors.accent};
`;

const ProjectLinks = styled.div`
  display: flex;
  gap: 1rem;
`;

const ProjectLink = styled(motion.a)`
  color: ${props => props.theme.colors.accent};
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  
  &:hover {
    text-decoration: underline;
  }
`;

const Modal = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 2rem;
`;

const ModalContent = styled(motion.div)`
  background: #1a1a1a;
  border-radius: 20px;
  padding: 2rem;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  border: 1px solid rgba(0, 255, 136, 0.3);
`;

const CloseButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: ${props => props.theme.colors.text};
  font-size: 1.5rem;
  cursor: pointer;
`;

// 3D Project Preview Component
function ProjectPreview({ isHovered }) {
  const meshRef = useRef();
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime) * 0.2;
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.3;
      meshRef.current.scale.setScalar(isHovered ? 1.2 : 1);
    }
  });

  return (
    <Box ref={meshRef} args={[1, 1, 1]}>
      <meshStandardMaterial color="#00ff88" wireframe />
    </Box>
  );
}

const Projects = () => {
  const [filter, setFilter] = useState('All');
  const [selectedProject, setSelectedProject] = useState(null);
  const [hoveredProject, setHoveredProject] = useState(null);

  const [titleRef, titleInView] = useIntersectionObserver({
    threshold: 0.3,
    triggerOnce: true,
  });

  const [projectsRef, projectsInView] = useIntersectionObserver({
    threshold: 0.2,
    triggerOnce: true,
  });

  const projects = [
    {
      id: 1,
      title: "E-Commerce Platform",
      description: "Full-stack e-commerce solution with React frontend and Laravel backend, featuring real-time inventory management and payment integration.",
      tech: ["React", "Laravel", "MySQL", "Stripe"],
      category: "Web App",
      github: "#",
      live: "#"
    },
    {
      id: 2,
      title: "3D Portfolio Website",
      description: "Interactive 3D portfolio showcasing Three.js capabilities with smooth animations and immersive user experience.",
      tech: ["Three.js", "React", "GSAP", "WebGL"],
      category: "3D/Animation",
      github: "#",
      live: "#"
    },
    {
      id: 3,
      title: "Task Management App",
      description: "Collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.",
      tech: ["Vue.js", "Node.js", "Socket.io", "MongoDB"],
      category: "Web App",
      github: "#",
      live: "#"
    },
    {
      id: 4,
      title: "Interactive Data Visualization",
      description: "Dynamic data visualization dashboard with interactive charts, real-time data updates, and responsive design.",
      tech: ["D3.js", "React", "Python", "FastAPI"],
      category: "Data Viz",
      github: "#",
      live: "#"
    },
    {
      id: 5,
      title: "Mobile App UI/UX",
      description: "Modern mobile application interface design with smooth animations and intuitive user experience.",
      tech: ["React Native", "Figma", "Lottie", "Expo"],
      category: "Mobile",
      github: "#",
      live: "#"
    },
    {
      id: 6,
      title: "WebGL Game",
      description: "Browser-based 3D game built with WebGL and Three.js, featuring physics simulation and interactive gameplay.",
      tech: ["Three.js", "WebGL", "Cannon.js", "JavaScript"],
      category: "3D/Animation",
      github: "#",
      live: "#"
    }
  ];

  const categories = ['All', 'Web App', '3D/Animation', 'Data Viz', 'Mobile'];

  const filteredProjects = filter === 'All' 
    ? projects 
    : projects.filter(project => project.category === filter);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <ProjectsSection id="projects">
      <Container>
        <SectionTitle
          ref={titleRef}
          initial={{ y: 50, opacity: 0 }}
          animate={titleInView ? { y: 0, opacity: 1 } : { y: 50, opacity: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          Featured Projects
        </SectionTitle>

        <FilterButtons>
          {categories.map((category) => (
            <FilterButton
              key={category}
              active={filter === category}
              onClick={() => setFilter(category)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {category}
            </FilterButton>
          ))}
        </FilterButtons>

        <ProjectsGrid
          ref={projectsRef}
          variants={containerVariants}
          initial="hidden"
          animate={projectsInView ? "visible" : "hidden"}
        >
          <AnimatePresence>
            {filteredProjects.map((project) => (
              <ProjectCard
                key={project.id}
                variants={itemVariants}
                layout
                whileHover={{ y: -10, scale: 1.02 }}
                onClick={() => setSelectedProject(project)}
                onMouseEnter={() => setHoveredProject(project.id)}
                onMouseLeave={() => setHoveredProject(null)}
              >
                <ProjectImage>
                  <CanvasContainer>
                    <Canvas camera={{ position: [0, 0, 3] }}>
                      <ambientLight intensity={0.5} />
                      <pointLight position={[10, 10, 10]} />
                      <ProjectPreview isHovered={hoveredProject === project.id} />
                    </Canvas>
                  </CanvasContainer>
                </ProjectImage>
                
                <ProjectContent>
                  <div>
                    <ProjectTitle>{project.title}</ProjectTitle>
                    <ProjectDescription>{project.description}</ProjectDescription>
                    <ProjectTech>
                      {project.tech.map((tech, index) => (
                        <TechTag key={index}>{tech}</TechTag>
                      ))}
                    </ProjectTech>
                  </div>
                  
                  <ProjectLinks>
                    <ProjectLink href={project.github} target="_blank">
                      🔗 GitHub
                    </ProjectLink>
                    <ProjectLink href={project.live} target="_blank">
                      🚀 Live Demo
                    </ProjectLink>
                  </ProjectLinks>
                </ProjectContent>
              </ProjectCard>
            ))}
          </AnimatePresence>
        </ProjectsGrid>
      </Container>

      <AnimatePresence>
        {selectedProject && (
          <Modal
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedProject(null)}
          >
            <ModalContent
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              <CloseButton onClick={() => setSelectedProject(null)}>×</CloseButton>
              <h2 style={{ color: '#00ff88', marginBottom: '1rem' }}>
                {selectedProject.title}
              </h2>
              <p style={{ color: '#a0a0a0', lineHeight: 1.6, marginBottom: '1.5rem' }}>
                {selectedProject.description}
              </p>
              <div style={{ marginBottom: '1.5rem' }}>
                <h3 style={{ color: '#fff', marginBottom: '0.5rem' }}>Technologies Used:</h3>
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem' }}>
                  {selectedProject.tech.map((tech, index) => (
                    <TechTag key={index}>{tech}</TechTag>
                  ))}
                </div>
              </div>
              <ProjectLinks>
                <ProjectLink href={selectedProject.github} target="_blank">
                  🔗 View on GitHub
                </ProjectLink>
                <ProjectLink href={selectedProject.live} target="_blank">
                  🚀 Live Demo
                </ProjectLink>
              </ProjectLinks>
            </ModalContent>
          </Modal>
        )}
      </AnimatePresence>
    </ProjectsSection>
  );
};

export default Projects;
