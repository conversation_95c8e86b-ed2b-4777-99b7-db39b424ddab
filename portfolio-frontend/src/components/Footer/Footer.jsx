import React from 'react';
import { motion } from 'framer-motion';
import styled from 'styled-components';

const FooterSection = styled.footer`
  background: #0a0a0a;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 3rem 2rem 1rem;
  position: relative;
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const FooterContent = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 2rem;

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }

  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
`;

const FooterColumn = styled(motion.div)`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FooterTitle = styled.h3`
  font-size: 1.2rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin-bottom: 0.5rem;
`;

const FooterText = styled.p`
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.6;
  font-size: 0.9rem;
`;

const FooterLink = styled(motion.a)`
  color: ${props => props.theme.colors.textSecondary};
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
  cursor: pointer;

  &:hover {
    color: ${props => props.theme.colors.accent};
  }
`;

const SocialLinks = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
`;

const SocialLink = styled(motion.a)`
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.theme.colors.textSecondary};
  text-decoration: none;
  font-size: 1.2rem;
  transition: all 0.3s ease;

  &:hover {
    background: ${props => props.theme.colors.accent};
    color: #000;
    border-color: ${props => props.theme.colors.accent};
    transform: translateY(-2px);
  }
`;

const FooterBottom = styled.div`
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;

  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    flex-direction: column;
    text-align: center;
  }
`;

const Copyright = styled.p`
  color: ${props => props.theme.colors.textSecondary};
  font-size: 0.9rem;
`;

const BackToTop = styled(motion.button)`
  background: linear-gradient(135deg, #00ff88, #00cc6a);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 255, 136, 0.3);
  }
`;

const Logo = styled(motion.div)`
  font-size: 1.5rem;
  font-weight: 700;
  color: ${props => props.theme.colors.accent};
  font-family: ${props => props.theme.fonts.secondary};
  margin-bottom: 1rem;
`;

const Footer = () => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToSection = (sectionId) => {
    const element = document.querySelector(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <FooterSection>
      <Container>
        <FooterContent
          as={motion.div}
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
        >
          <FooterColumn variants={itemVariants}>
            <Logo
              whileHover={{ scale: 1.05 }}
              onClick={() => scrollToSection('#home')}
            >
              DVS CodeCraft
            </Logo>
            <FooterText>
              Creating immersive digital experiences with cutting-edge technology. 
              Specializing in modern web development, interactive animations, and 3D visualizations.
            </FooterText>
            <FooterText>
              <strong>Designed & Developed by DVS Codecraft</strong>
            </FooterText>
            <SocialLinks>
              <SocialLink
                href="#"
                target="_blank"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                📧
              </SocialLink>
              <SocialLink
                href="#"
                target="_blank"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                💼
              </SocialLink>
              <SocialLink
                href="#"
                target="_blank"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                🐙
              </SocialLink>
              <SocialLink
                href="#"
                target="_blank"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                🐦
              </SocialLink>
            </SocialLinks>
          </FooterColumn>

          <FooterColumn variants={itemVariants}>
            <FooterTitle>Navigation</FooterTitle>
            <FooterLink
              onClick={() => scrollToSection('#home')}
              whileHover={{ x: 5 }}
            >
              Home
            </FooterLink>
            <FooterLink
              onClick={() => scrollToSection('#about')}
              whileHover={{ x: 5 }}
            >
              About
            </FooterLink>
            <FooterLink
              onClick={() => scrollToSection('#skills')}
              whileHover={{ x: 5 }}
            >
              Skills
            </FooterLink>
            <FooterLink
              onClick={() => scrollToSection('#projects')}
              whileHover={{ x: 5 }}
            >
              Projects
            </FooterLink>
            <FooterLink
              onClick={() => scrollToSection('#contact')}
              whileHover={{ x: 5 }}
            >
              Contact
            </FooterLink>
          </FooterColumn>

          <FooterColumn variants={itemVariants}>
            <FooterTitle>Services</FooterTitle>
            <FooterLink whileHover={{ x: 5 }}>Web Development</FooterLink>
            <FooterLink whileHover={{ x: 5 }}>3D Visualization</FooterLink>
            <FooterLink whileHover={{ x: 5 }}>UI/UX Design</FooterLink>
            <FooterLink whileHover={{ x: 5 }}>Mobile Apps</FooterLink>
            <FooterLink whileHover={{ x: 5 }}>Consulting</FooterLink>
          </FooterColumn>

          <FooterColumn variants={itemVariants}>
            <FooterTitle>Technologies</FooterTitle>
            <FooterLink whileHover={{ x: 5 }}>React & Vue.js</FooterLink>
            <FooterLink whileHover={{ x: 5 }}>Laravel & Node.js</FooterLink>
            <FooterLink whileHover={{ x: 5 }}>Three.js & WebGL</FooterLink>
            <FooterLink whileHover={{ x: 5 }}>GSAP & Framer Motion</FooterLink>
            <FooterLink whileHover={{ x: 5 }}>AWS & Docker</FooterLink>
          </FooterColumn>
        </FooterContent>

        <FooterBottom>
          <Copyright>
            © 2024 DVS CodeCraft. All rights reserved. Built with ❤️ in Georgia.
          </Copyright>
          
          <BackToTop
            onClick={scrollToTop}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            ↑
          </BackToTop>
        </FooterBottom>
      </Container>
    </FooterSection>
  );
};

export default Footer;
