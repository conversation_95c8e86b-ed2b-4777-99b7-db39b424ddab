import {
  Can<PERSON>,
  _roots,
  act,
  addAfterEffect,
  addEffect,
  addTail,
  advance,
  applyProps,
  buildGraph,
  context,
  createEvents,
  createPointerEvents,
  createPortal,
  createRoot,
  dispose,
  extend,
  flushGlobalEffects,
  flushSync,
  getRootState,
  invalidate,
  reconciler,
  threeTypes,
  unmountComponentAtNode,
  useFrame,
  useGraph,
  useInstanceHandle,
  useLoader,
  useStore,
  useThree
} from "./chunk-SVNHOHNN.js";
import "./chunk-QMTJ6FNX.js";
import "./chunk-VTIQK5XW.js";
import "./chunk-H5FQS3OF.js";
import "./chunk-V4OQ3NZ2.js";
export {
  Canvas,
  threeTypes as ReactThreeFiber,
  _roots,
  act,
  addAfterEffect,
  addEffect,
  addTail,
  advance,
  applyProps,
  buildGraph,
  context,
  createEvents,
  createPortal,
  createRoot,
  dispose,
  createPointerEvents as events,
  extend,
  flushGlobalEffects,
  flushSync,
  getRootState,
  invalidate,
  reconciler,
  unmountComponentAtNode,
  useFrame,
  useGraph,
  useInstanceHand<PERSON>,
  useLoader,
  useStore,
  useThree
};
//# sourceMappingURL=@react-three_fiber.js.map
