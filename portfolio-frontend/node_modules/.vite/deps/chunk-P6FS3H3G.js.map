{"version": 3, "sources": ["../../maath/dist/objectSpread2-284232a6.esm.js", "../../maath/dist/classCallCheck-9098b006.esm.js", "../../maath/dist/isNativeReflectConstruct-5594d075.esm.js", "../../maath/dist/matrix-baa530bf.esm.js", "../../maath/dist/triangle-b62b9067.esm.js", "../../maath/dist/misc-19a3ec46.esm.js", "../../maath/dist/index-0332b2ed.esm.js"], "sourcesContent": ["function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nexport { _objectSpread2 as _, _defineProperty as a };\n", "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nexport { _classCallCheck as _ };\n", "function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(<PERSON>olean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport { _setPrototypeOf as _, _isNativeReflectConstruct as a };\n", "import { Matrix3 } from 'three';\n\n/**\n *\n * @param terms\n *\n * | a b |\n * | c d |\n *\n * @returns {number} determinant\n */\n\nfunction determinant2() {\n  for (var _len = arguments.length, terms = new Array(_len), _key = 0; _key < _len; _key++) {\n    terms[_key] = arguments[_key];\n  }\n\n  var a = terms[0],\n      b = terms[1],\n      c = terms[2],\n      d = terms[3];\n  return a * d - b * c;\n}\n/**\n *\n * @param terms\n *\n * | a b c |\n * | d e f |\n * | g h i |\n *\n * @returns {number} determinant\n */\n\nfunction determinant3() {\n  for (var _len2 = arguments.length, terms = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    terms[_key2] = arguments[_key2];\n  }\n\n  var a = terms[0],\n      b = terms[1],\n      c = terms[2],\n      d = terms[3],\n      e = terms[4],\n      f = terms[5],\n      g = terms[6],\n      h = terms[7],\n      i = terms[8];\n  return a * e * i + b * f * g + c * d * h - c * e * g - b * d * i - a * f * h;\n}\n/**\n *\n * @param terms\n *\n * | a b c g |\n * | h i j k |\n * | l m n o |\n *\n * @returns {number} determinant\n */\n\nfunction determinant4() {\n  for (var _len3 = arguments.length, terms = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n    terms[_key3] = arguments[_key3];\n  }\n\n  terms[0];\n      terms[1];\n      terms[2];\n      terms[3];\n      terms[4];\n      terms[5];\n      terms[6];\n      terms[7];\n      terms[8];\n      terms[9];\n      terms[10];\n      terms[11];\n      terms[12];\n      terms[13];\n      terms[14]; // TODO\n}\n/**\n *\n * Get the determinant of matrix m without row r and col c\n *\n * @param {matrix} m Starter matrix\n * @param r row to remove\n * @param c col to remove\n *\n *     | a b c |\n * m = | d e f |\n *     | g h i |\n *\n * getMinor(m, 1, 1) would result in this determinant\n *\n * | a c |\n * | g i |\n *\n * @returns {number} determinant\n */\n\nfunction getMinor(matrix, r, c) {\n  var _matrixTranspose = matrix.clone().transpose();\n\n  var x = [];\n  var l = _matrixTranspose.elements.length;\n  var n = Math.sqrt(l);\n\n  for (var i = 0; i < l; i++) {\n    var element = _matrixTranspose.elements[i];\n    var row = Math.floor(i / n);\n    var col = i % n;\n\n    if (row !== r - 1 && col !== c - 1) {\n      x.push(element);\n    }\n  }\n\n  return determinant3.apply(void 0, x);\n}\n/**\n *\n */\n\nfunction matrixSum3(m1, m2) {\n  var sum = [];\n  var m1Array = m1.toArray();\n  var m2Array = m2.toArray();\n\n  for (var i = 0; i < m1Array.length; i++) {\n    sum[i] = m1Array[i] + m2Array[i];\n  }\n\n  return new Matrix3().fromArray(sum);\n}\n\nvar matrix = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  determinant2: determinant2,\n  determinant3: determinant3,\n  determinant4: determinant4,\n  getMinor: getMinor,\n  matrixSum3: matrixSum3\n});\n\nexport { matrixSum3 as a, determinant2 as b, determinant4 as c, determinant3 as d, getMinor as g, matrix as m };\n", "import { a as _isNativeReflectConstruct, _ as _setPrototypeOf } from './isNativeReflectConstruct-5594d075.esm.js';\nimport { Vector2, Matrix4 } from 'three';\nimport { d as determinant3, g as getMinor } from './matrix-baa530bf.esm.js';\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n\n  var _s, _e;\n\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _construct(Parent, args, Class) {\n  if (_isNativeReflectConstruct()) {\n    _construct = Reflect.construct;\n  } else {\n    _construct = function _construct(Parent, args, Class) {\n      var a = [null];\n      a.push.apply(a, args);\n      var Constructor = Function.bind.apply(Parent, a);\n      var instance = new Constructor();\n      if (Class) _setPrototypeOf(instance, Class.prototype);\n      return instance;\n    };\n  }\n\n  return _construct.apply(null, arguments);\n}\n\n/**\n *\n * @param point\n *\n * @param triangle\n *\n * @returns {boolean} true if the point is in the triangle\n *\n * TODO: Find explainer\n */\nfunction isPointInTriangle(point, triangle) {\n  var _triangle$ = _slicedToArray(triangle[0], 2),\n      ax = _triangle$[0],\n      ay = _triangle$[1];\n\n  var _triangle$2 = _slicedToArray(triangle[1], 2),\n      bx = _triangle$2[0],\n      by = _triangle$2[1];\n\n  var _triangle$3 = _slicedToArray(triangle[2], 2),\n      cx = _triangle$3[0],\n      cy = _triangle$3[1];\n\n  var _point = _slicedToArray(point, 2),\n      px = _point[0],\n      py = _point[1]; // TODO Sub with static calc\n\n\n  var matrix = new Matrix4(); // prettier-ignore\n\n  matrix.set(ax, ay, ax * ax + ay * ay, 1, bx, by, bx * bx + by * by, 1, cx, cy, cx * cx + cy * cy, 1, px, py, px * px + py * py, 1);\n  return matrix.determinant() <= 0;\n}\nfunction triangleDeterminant(triangle) {\n  var _triangle$4 = _slicedToArray(triangle[0], 2),\n      x1 = _triangle$4[0],\n      y1 = _triangle$4[1];\n\n  var _triangle$5 = _slicedToArray(triangle[1], 2),\n      x2 = _triangle$5[0],\n      y2 = _triangle$5[1];\n\n  var _triangle$6 = _slicedToArray(triangle[2], 2),\n      x3 = _triangle$6[0],\n      y3 = _triangle$6[1]; // prettier-ignore\n\n\n  return determinant3(x1, y1, 1, x2, y2, 1, x3, y3, 1);\n}\n/**\n * Uses triangle area determinant to check if 3 points are collinear.\n * If they are, they can't make a triangle, so the determinant will be 0!\n *\n *      0     1     2\n * ─────■─────■─────■\n *\n *\n * Fun fact, you can use this same determinant to check the order of the points in the triangle\n *\n * NOTE: Should this use a buffer instead? NOTE: Should this use a buffer instead? [x0, y0, x1, y1, x2, y2]?\n *\n */\n\nfunction arePointsCollinear(points) {\n  return triangleDeterminant(points) === 0;\n} // TODO This is the same principle as the prev function, find a way to make it have sense\n\nfunction isTriangleClockwise(triangle) {\n  return triangleDeterminant(triangle) < 0;\n}\n/**\n \nThe circumcircle is a circle touching all the vertices of a triangle or polygon.\n\n             ┌───┐             \n             │ B │             \n             └───┘             \n           .───●───.           \n        ,─'   ╱ ╲   '─.        \n      ,'     ╱   ╲     `.      \n     ╱      ╱     ╲      ╲     \n    ;      ╱       ╲      :    \n    │     ╱         ╲     │    \n    │    ╱           ╲    │    \n    :   ╱             ╲   ;    \n     ╲ ╱               ╲ ╱     \n┌───┐ ●─────────────────● ┌───┐\n│ A │  `.             ,'  │ C │\n└───┘    '─.       ,─'    └───┘\n            `─────'                         \n */\n\n/**\n *\n * @param triangle\n *\n * @returns {number} circumcircle\n */\n// https://math.stackexchange.com/a/1460096\n\nfunction getCircumcircle(triangle) {\n  // TS-TODO the next few lines are ignored because the types aren't current to the change in vectors (that can now be iterated)\n  // @ts-ignore\n  var _triangle$7 = _slicedToArray(triangle[0], 2),\n      ax = _triangle$7[0],\n      ay = _triangle$7[1]; // @ts-ignore\n\n\n  var _triangle$8 = _slicedToArray(triangle[1], 2),\n      bx = _triangle$8[0],\n      by = _triangle$8[1]; // @ts-ignore\n\n\n  var _triangle$9 = _slicedToArray(triangle[2], 2),\n      cx = _triangle$9[0],\n      cy = _triangle$9[1];\n\n  if (arePointsCollinear(triangle)) return null; // points are collinear\n\n  var m = new Matrix4(); // prettier-ignore\n\n  m.set(1, 1, 1, 1, ax * ax + ay * ay, ax, ay, 1, bx * bx + by * by, bx, by, 1, cx * cx + cy * cy, cx, cy, 1);\n  var m11 = getMinor(m, 1, 1);\n  var m13 = getMinor(m, 1, 3);\n  var m12 = getMinor(m, 1, 2);\n  var m14 = getMinor(m, 1, 4);\n  var x0 = 0.5 * (m12 / m11);\n  var y0 = 0.5 * (m13 / m11);\n  var r2 = x0 * x0 + y0 * y0 + m14 / m11;\n  return {\n    x: Math.abs(x0) === 0 ? 0 : x0,\n    y: Math.abs(y0) === 0 ? 0 : -y0,\n    r: Math.sqrt(r2)\n  };\n} // https://stackoverflow.com/questions/39984709/how-can-i-check-wether-a-point-is-inside-the-circumcircle-of-3-points\n\nfunction isPointInCircumcircle(point, triangle) {\n  var _ref = Array.isArray(triangle[0]) ? triangle[0] : triangle[0].toArray(),\n      _ref2 = _slicedToArray(_ref, 2),\n      ax = _ref2[0],\n      ay = _ref2[1];\n\n  var _ref3 = Array.isArray(triangle[1]) ? triangle[1] : triangle[1].toArray(),\n      _ref4 = _slicedToArray(_ref3, 2),\n      bx = _ref4[0],\n      by = _ref4[1];\n\n  var _ref5 = Array.isArray(triangle[2]) ? triangle[2] : triangle[2].toArray(),\n      _ref6 = _slicedToArray(_ref5, 2),\n      cx = _ref6[0],\n      cy = _ref6[1];\n\n  var _point2 = _slicedToArray(point, 2),\n      px = _point2[0],\n      py = _point2[1];\n\n  if (arePointsCollinear(triangle)) throw new Error(\"Collinear points don't form a triangle\");\n  /**\n          | ax-px, ay-py, (ax-px)² + (ay-py)² |\n    det = | bx-px, by-py, (bx-px)² + (by-py)² |\n          | cx-px, cy-py, (cx-px)² + (cy-py)² |\n  */\n\n  var x1mpx = ax - px;\n  var aympy = ay - py;\n  var bxmpx = bx - px;\n  var bympy = by - py;\n  var cxmpx = cx - px;\n  var cympy = cy - py; // prettier-ignore\n\n  var d = determinant3(x1mpx, aympy, x1mpx * x1mpx + aympy * aympy, bxmpx, bympy, bxmpx * bxmpx + bympy * bympy, cxmpx, cympy, cxmpx * cxmpx + cympy * cympy); // if d is 0, the point is on C\n\n  if (d === 0) {\n    return true;\n  }\n\n  return !isTriangleClockwise(triangle) ? d > 0 : d < 0;\n} // From https://algorithmtutor.com/Computational-Geometry/Determining-if-two-consecutive-segments-turn-left-or-right/\n\nvar mv1 = new Vector2();\nvar mv2 = new Vector2();\n/**\n \n     ╱      ╲     \n    ╱        ╲    \n   ▕          ▏   \n                  \n right      left  \n\n * NOTE: Should this use a buffer instead? [x0, y0, x1, y1]?\n */\n\nfunction doThreePointsMakeARight(points) {\n  var _points$map = points.map(function (p) {\n    if (Array.isArray(p)) {\n      return _construct(Vector2, _toConsumableArray(p));\n    }\n\n    return p;\n  }),\n      _points$map2 = _slicedToArray(_points$map, 3),\n      p1 = _points$map2[0],\n      p2 = _points$map2[1],\n      p3 = _points$map2[2];\n\n  if (arePointsCollinear(points)) return false; // @ts-ignore\n\n  var p2p1 = mv1.subVectors(p2, p1); // @ts-ignore\n\n  var p3p1 = mv2.subVectors(p3, p1);\n  var cross = p3p1.cross(p2p1);\n  return cross > 0;\n}\n\nvar triangle = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  isPointInTriangle: isPointInTriangle,\n  triangleDeterminant: triangleDeterminant,\n  arePointsCollinear: arePointsCollinear,\n  isTriangleClockwise: isTriangleClockwise,\n  getCircumcircle: getCircumcircle,\n  isPointInCircumcircle: isPointInCircumcircle,\n  doThreePointsMakeARight: doThreePointsMakeARight\n});\n\nexport { _slicedToArray as _, _toConsumableArray as a, triangleDeterminant as b, arePointsCollinear as c, doThreePointsMakeARight as d, isTriangleClockwise as e, isPointInCircumcircle as f, getCircumcircle as g, isPointInTriangle as i, triangle as t };\n", "import { d as doThreePointsMakeARight, a as _toConsumableArray, _ as _slicedToArray } from './triangle-b62b9067.esm.js';\nimport { Vector3, Matrix3 } from 'three';\nimport { a as matrixSum3 } from './matrix-baa530bf.esm.js';\n\n/**\n * Clamps a value between a range.\n */\nfunction clamp(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n} // Loops the value t, so that it is never larger than length and never smaller than 0.\n\nfunction repeat(t, length) {\n  return clamp(t - Math.floor(t / length) * length, 0, length);\n} // Calculates the shortest difference between two given angles.\n\nfunction deltaAngle(current, target) {\n  var delta = repeat(target - current, Math.PI * 2);\n  if (delta > Math.PI) delta -= Math.PI * 2;\n  return delta;\n}\n/**\n * Converts degrees to radians.\n */\n\nfunction degToRad(degrees) {\n  return degrees / 180 * Math.PI;\n}\n/**\n * Converts radians to degrees.\n */\n\nfunction radToDeg(radians) {\n  return radians * 180 / Math.PI;\n} // adapted from https://gist.github.com/stephanbogner/a5f50548a06bec723dcb0991dcbb0856 by https://twitter.com/st_phan\n\nfunction fibonacciOnSphere(buffer, _ref) {\n  var _ref$radius = _ref.radius,\n      radius = _ref$radius === void 0 ? 1 : _ref$radius;\n  var samples = buffer.length / 3;\n  var offset = 2 / samples;\n  var increment = Math.PI * (3 - 2.2360679775);\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    var y = i * offset - 1 + offset / 2;\n    var distance = Math.sqrt(1 - Math.pow(y, 2));\n    var phi = i % samples * increment;\n    var x = Math.cos(phi) * distance;\n    var z = Math.sin(phi) * distance;\n    buffer[i] = x * radius;\n    buffer[i + 1] = y * radius;\n    buffer[i + 2] = z * radius;\n  }\n} // @ts-ignore\n\nfunction vectorEquals(a, b) {\n  var eps = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Number.EPSILON;\n  return Math.abs(a.x - b.x) < eps && Math.abs(a.y - b.y) < eps && Math.abs(a.z - b.z) < eps;\n}\n/**\n * Sorts vectors in lexicographic order, works with both v2 and v3\n *\n *  Use as:\n *  const sorted = arrayOfVectors.sort(lexicographicOrder)\n */\n// https://en.wikipedia.org/wiki/Lexicographic_order\n\nfunction lexicographic(a, b) {\n  if (a.x === b.x) {\n    // do a check to see if points is 3D,\n    // in which case add y eq check and sort by z\n    if (typeof a.z !== \"undefined\") {\n      if (a.y === b.y) {\n        return a.z - b.z;\n      }\n    }\n\n    return a.y - b.y;\n  }\n\n  return a.x - b.x;\n}\n/**\n * Convex Hull\n *\n * Returns an array of 2D Vectors representing the convex hull of a set of 2D Vectors\n */\n\n/**\n * Calculate the convex hull of a set of points\n */\n\nfunction convexHull(_points) {\n  var points = _points.sort(lexicographic); // put p1 and p2 in a list lUpper with p1 as the first point\n\n\n  var lUpper = [points[0], points[1]]; // for i <- 3 to n\n\n  for (var i = 2; i < points.length; i++) {\n    lUpper.push(points[i]); // while lUpper contains more than 2 points and the last three points in lUpper do not make a right turn\n\n    while (lUpper.length > 2 && doThreePointsMakeARight(_toConsumableArray(lUpper.slice(-3)))) {\n      // delete the middle of the last three points from lUpper\n      lUpper.splice(lUpper.length - 2, 1);\n    }\n  } // put pn and pn-1 in a list lLower with pn as the first point\n\n\n  var lLower = [points[points.length - 1], points[points.length - 2]]; // for (i <- n - 2 downto 1)\n\n  for (var _i = points.length - 3; _i >= 0; _i--) {\n    // append pi to lLower\n    lLower.push(points[_i]); // while lLower contains more than 2 points and the last three points in lLower do not make a right turn\n\n    while (lLower.length > 2 && doThreePointsMakeARight(_toConsumableArray(lLower.slice(-3)))) {\n      // delete the middle of the last three points from lLower\n      lLower.splice(lLower.length - 2, 1);\n    }\n  } // remove the first and last point from lLower to avoid duplication of the points where the upper and lower hull meet\n\n\n  lLower.splice(0, 1);\n  lLower.splice(lLower.length - 1, 1); // prettier-ignore\n\n  var c = [].concat(lUpper, lLower);\n  return c;\n}\nfunction remap(x, _ref2, _ref3) {\n  var _ref4 = _slicedToArray(_ref2, 2),\n      low1 = _ref4[0],\n      high1 = _ref4[1];\n\n  var _ref5 = _slicedToArray(_ref3, 2),\n      low2 = _ref5[0],\n      high2 = _ref5[1];\n\n  return low2 + (x - low1) * (high2 - low2) / (high1 - low1);\n}\n/**\n *\n * https://www.desmos.com/calculator/vsnmlaljdu\n *\n * Ease-in-out, goes to -Infinite before 0 and Infinite after 1\n *\n * @param t\n * @returns\n */\n\nfunction fade(t) {\n  return t * t * t * (t * (t * 6 - 15) + 10);\n}\n/**\n *\n * Returns the result of linearly interpolating between input A and input B by input T.\n *\n * @param v0\n * @param v1\n * @param t\n * @returns\n */\n\nfunction lerp(v0, v1, t) {\n  return v0 * (1 - t) + v1 * t;\n}\n/**\n *\n * Returns the linear parameter that produces the interpolant specified by input T within the range of input A to input B.\n *\n * @param v0\n * @param v1\n * @param t\n * @returns\n */\n\nfunction inverseLerp(v0, v1, t) {\n  return (t - v0) / (v1 - v0);\n}\n/**\n *\n */\n\nfunction normalize(x, y, z) {\n  var m = Math.sqrt(x * x + y * y + z * z);\n  return [x / m, y / m, z / m];\n}\n/**\n *\n */\n\nfunction pointOnCubeToPointOnSphere(x, y, z) {\n  var x2 = x * x;\n  var y2 = y * y;\n  var z2 = z * z;\n  var nx = x * Math.sqrt(1 - (y2 + z2) / 2 + y2 * z2 / 3);\n  var ny = y * Math.sqrt(1 - (z2 + x2) / 2 + z2 * x2 / 3);\n  var nz = z * Math.sqrt(1 - (x2 + y2) / 2 + x2 * y2 / 3);\n  return [nx, ny, nz];\n} // https://math.stackexchange.com/questions/180418/calculate-rotation-matrix-to-align-vector-a-to-vector-b-in-3d\n\n/**\n * Give two unit vectors a and b, returns the transformation matrix that rotates a onto b.\n *\n * */\n\nfunction rotateVectorOnVector(a, b) {\n  var v = new Vector3().crossVectors(a, b);\n  var c = a.dot(b);\n  var i = new Matrix3().identity(); //  skew-symmetric cross-product matrix of 𝑣 https://en.wikipedia.org/wiki/Skew-symmetric_matrix\n  // prettier-ignore\n\n  var vx = new Matrix3().set(0, -v.z, v.y, v.z, 0, -v.x, -v.y, v.x, 0);\n  var vxsquared = new Matrix3().multiplyMatrices(vx, vx).multiplyScalar(1 / (1 + c));\n\n  var _final = matrixSum3(matrixSum3(i, vx), vxsquared);\n\n  return _final;\n} // calculate latitude and longitude (in radians) from point on unit sphere\n\nfunction pointToCoordinate(x, y, z) {\n  var lat = Math.asin(y);\n  var lon = Math.atan2(x, -z);\n  return [lat, lon];\n} // calculate point on unit sphere given latitude and logitude in radians\n\nfunction coordinateToPoint(lat, lon) {\n  var y = Math.sin(lat);\n  var r = Math.cos(lat);\n  var x = Math.sin(lon) * r;\n  var z = -Math.cos(lon) * r;\n  return [x, y, z];\n}\n/**\n * Given a plane and a segment, return the intersection point if it exists or null it doesn't.\n */\n\nfunction planeSegmentIntersection(plane, segment) {\n  var _segment = _slicedToArray(segment, 2),\n      a = _segment[0],\n      b = _segment[1];\n\n  var matrix = rotateVectorOnVector(plane.normal, new Vector3(0, 1, 0));\n  var t = inverseLerp(a.clone().applyMatrix3(matrix).y, b.clone().applyMatrix3(matrix).y, 0);\n  return new Vector3().lerpVectors(a, b, t);\n}\n/**\n * Given a plane and a point, return the distance.\n */\n\nfunction pointToPlaneDistance(p, plane) {\n  var d = plane.normal.dot(p); // TODO\n\n  return d;\n}\nfunction getIndexFrom3D(coords, sides) {\n  var _coords = _slicedToArray(coords, 3),\n      ix = _coords[0],\n      iy = _coords[1],\n      iz = _coords[2];\n\n  var _sides = _slicedToArray(sides, 2),\n      rx = _sides[0],\n      ry = _sides[1];\n\n  return iz * rx * ry + iy * rx + ix;\n}\nfunction get3DFromIndex(index, size) {\n  var _size = _slicedToArray(size, 2),\n      rx = _size[0],\n      ry = _size[1];\n\n  var a = rx * ry;\n  var z = index / a;\n  var b = index - a * z;\n  var y = b / rx;\n  var x = b % rx;\n  return [x, y, z];\n}\nfunction getIndexFrom2D(coords, size) {\n  return coords[0] + size[0] * coords[1];\n}\nfunction get2DFromIndex(index, columns) {\n  var x = index % columns;\n  var y = Math.floor(index / columns);\n  return [x, y];\n}\n\nvar misc = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  clamp: clamp,\n  repeat: repeat,\n  deltaAngle: deltaAngle,\n  degToRad: degToRad,\n  radToDeg: radToDeg,\n  fibonacciOnSphere: fibonacciOnSphere,\n  vectorEquals: vectorEquals,\n  lexicographic: lexicographic,\n  convexHull: convexHull,\n  remap: remap,\n  fade: fade,\n  lerp: lerp,\n  inverseLerp: inverseLerp,\n  normalize: normalize,\n  pointOnCubeToPointOnSphere: pointOnCubeToPointOnSphere,\n  rotateVectorOnVector: rotateVectorOnVector,\n  pointToCoordinate: pointToCoordinate,\n  coordinateToPoint: coordinateToPoint,\n  planeSegmentIntersection: planeSegmentIntersection,\n  pointToPlaneDistance: pointToPlaneDistance,\n  getIndexFrom3D: getIndexFrom3D,\n  get3DFromIndex: get3DFromIndex,\n  getIndexFrom2D: getIndexFrom2D,\n  get2DFromIndex: get2DFromIndex\n});\n\nexport { degToRad as a, radToDeg as b, clamp as c, deltaAngle as d, fibonacciOnSphere as e, fade as f, lexicographic as g, convexHull as h, remap as i, inverseLerp as j, rotateVectorOnVector as k, lerp as l, misc as m, normalize as n, pointToCoordinate as o, pointOnCubeToPointOnSphere as p, coordinateToPoint as q, repeat as r, planeSegmentIntersection as s, pointToPlaneDistance as t, getIndexFrom3D as u, vectorEquals as v, get3DFromIndex as w, getIndexFrom2D as x, get2DFromIndex as y };\n", "import { a as _defineProperty, _ as _objectSpread2 } from './objectSpread2-284232a6.esm.js';\nimport { _ as _classCallCheck } from './classCallCheck-9098b006.esm.js';\nimport { l as lerp, f as fade } from './misc-19a3ec46.esm.js';\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\n/*\n * A speed-improved perlin and simplex noise algorithms for 2D.\n *\n * Based on example code by <PERSON> (<EMAIL>).\n * Optimisations by <PERSON> (p<PERSON><PERSON>@drizzle.stanford.edu).\n * Better rank ordering method by <PERSON> in 2012.\n * Converted to Javascript by <PERSON>tle.\n *\n * Version 2012-03-09\n *\n * This code was placed in the public domain by its original author,\n * Stefan Gustavson. You may use it as you see fit, but\n * attribution is appreciated.\n *\n */\n\nvar Grad = function Grad(x, y, z) {\n  var _this = this;\n\n  _classCallCheck(this, Grad);\n\n  _defineProperty(this, \"dot2\", function (x, y) {\n    return _this.x * x + _this.y * y;\n  });\n\n  _defineProperty(this, \"dot3\", function (x, y, z) {\n    return _this.x * x + _this.y * y + _this.z * z;\n  });\n\n  this.x = x;\n  this.y = y;\n  this.z = z;\n};\n\nvar grad3 = [new Grad(1, 1, 0), new Grad(-1, 1, 0), new Grad(1, -1, 0), new Grad(-1, -1, 0), new Grad(1, 0, 1), new Grad(-1, 0, 1), new Grad(1, 0, -1), new Grad(-1, 0, -1), new Grad(0, 1, 1), new Grad(0, -1, 1), new Grad(0, 1, -1), new Grad(0, -1, -1)];\nvar p = [151, 160, 137, 91, 90, 15, 131, 13, 201, 95, 96, 53, 194, 233, 7, 225, 140, 36, 103, 30, 69, 142, 8, 99, 37, 240, 21, 10, 23, 190, 6, 148, 247, 120, 234, 75, 0, 26, 197, 62, 94, 252, 219, 203, 117, 35, 11, 32, 57, 177, 33, 88, 237, 149, 56, 87, 174, 20, 125, 136, 171, 168, 68, 175, 74, 165, 71, 134, 139, 48, 27, 166, 77, 146, 158, 231, 83, 111, 229, 122, 60, 211, 133, 230, 220, 105, 92, 41, 55, 46, 245, 40, 244, 102, 143, 54, 65, 25, 63, 161, 1, 216, 80, 73, 209, 76, 132, 187, 208, 89, 18, 169, 200, 196, 135, 130, 116, 188, 159, 86, 164, 100, 109, 198, 173, 186, 3, 64, 52, 217, 226, 250, 124, 123, 5, 202, 38, 147, 118, 126, 255, 82, 85, 212, 207, 206, 59, 227, 47, 16, 58, 17, 182, 189, 28, 42, 223, 183, 170, 213, 119, 248, 152, 2, 44, 154, 163, 70, 221, 153, 101, 155, 167, 43, 172, 9, 129, 22, 39, 253, 19, 98, 108, 110, 79, 113, 224, 232, 178, 185, 112, 104, 218, 246, 97, 228, 251, 34, 242, 193, 238, 210, 144, 12, 191, 179, 162, 241, 81, 51, 145, 235, 249, 14, 239, 107, 49, 192, 214, 31, 181, 199, 106, 157, 184, 84, 204, 176, 115, 121, 50, 45, 127, 4, 150, 254, 138, 236, 205, 93, 222, 114, 67, 29, 24, 72, 243, 141, 128, 195, 78, 66, 215, 61, 156, 180]; // To remove the need for index wrapping, double the permutation table length\n\nvar perm = new Array(512);\nvar gradP = new Array(512); // This isn't a very good seeding function, but it works ok. It supports 2^16\n// different seed values. Write something better if you need more seeds.\n\nvar seed = function seed(_seed) {\n  if (_seed > 0 && _seed < 1) {\n    // Scale the seed out\n    _seed *= 65536;\n  }\n\n  _seed = Math.floor(_seed);\n\n  if (_seed < 256) {\n    _seed |= _seed << 8;\n  }\n\n  for (var i = 0; i < 256; i++) {\n    var v;\n\n    if (i & 1) {\n      v = p[i] ^ _seed & 255;\n    } else {\n      v = p[i] ^ _seed >> 8 & 255;\n    }\n\n    perm[i] = perm[i + 256] = v;\n    gradP[i] = gradP[i + 256] = grad3[v % 12];\n  }\n};\nseed(0);\n/*\n  for(var i=0; i<256; i++) {\n    perm[i] = perm[i + 256] = p[i];\n    gradP[i] = gradP[i + 256] = grad3[perm[i] % 12];\n  }*/\n// Skewing and unskewing factors for 2, 3, and 4 dimensions\n\nvar F2 = 0.5 * (Math.sqrt(3) - 1);\nvar G2 = (3 - Math.sqrt(3)) / 6;\nvar F3 = 1 / 3;\nvar G3 = 1 / 6; // 2D simplex noise\n\nvar simplex2 = function simplex2(xin, yin) {\n  var n0, n1, n2; // Noise contributions from the three corners\n  // Skew the input space to determine which simplex cell we're in\n\n  var s = (xin + yin) * F2; // Hairy factor for 2D\n\n  var i = Math.floor(xin + s);\n  var j = Math.floor(yin + s);\n  var t = (i + j) * G2;\n  var x0 = xin - i + t; // The x,y distances from the cell origin, unskewed.\n\n  var y0 = yin - j + t; // For the 2D case, the simplex shape is an equilateral triangle.\n  // Determine which simplex we are in.\n\n  var i1, j1; // Offsets for second (middle) corner of simplex in (i,j) coords\n\n  if (x0 > y0) {\n    // lower triangle, XY order: (0,0)->(1,0)->(1,1)\n    i1 = 1;\n    j1 = 0;\n  } else {\n    // upper triangle, YX order: (0,0)->(0,1)->(1,1)\n    i1 = 0;\n    j1 = 1;\n  } // A step of (1,0) in (i,j) means a step of (1-c,-c) in (x,y), and\n  // a step of (0,1) in (i,j) means a step of (-c,1-c) in (x,y), where\n  // c = (3-sqrt(3))/6\n\n\n  var x1 = x0 - i1 + G2; // Offsets for middle corner in (x,y) unskewed coords\n\n  var y1 = y0 - j1 + G2;\n  var x2 = x0 - 1 + 2 * G2; // Offsets for last corner in (x,y) unskewed coords\n\n  var y2 = y0 - 1 + 2 * G2; // Work out the hashed gradient indices of the three simplex corners\n\n  i &= 255;\n  j &= 255;\n  var gi0 = gradP[i + perm[j]];\n  var gi1 = gradP[i + i1 + perm[j + j1]];\n  var gi2 = gradP[i + 1 + perm[j + 1]]; // Calculate the contribution from the three corners\n\n  var t0 = 0.5 - x0 * x0 - y0 * y0;\n\n  if (t0 < 0) {\n    n0 = 0;\n  } else {\n    t0 *= t0;\n    n0 = t0 * t0 * gi0.dot2(x0, y0); // (x,y) of grad3 used for 2D gradient\n  }\n\n  var t1 = 0.5 - x1 * x1 - y1 * y1;\n\n  if (t1 < 0) {\n    n1 = 0;\n  } else {\n    t1 *= t1;\n    n1 = t1 * t1 * gi1.dot2(x1, y1);\n  }\n\n  var t2 = 0.5 - x2 * x2 - y2 * y2;\n\n  if (t2 < 0) {\n    n2 = 0;\n  } else {\n    t2 *= t2;\n    n2 = t2 * t2 * gi2.dot2(x2, y2);\n  } // Add contributions from each corner to get the final noise value.\n  // The result is scaled to return values in the interval [-1,1].\n\n\n  return 70 * (n0 + n1 + n2);\n}; // 3D simplex noise\n\nvar simplex3 = function simplex3(xin, yin, zin) {\n  var n0, n1, n2, n3; // Noise contributions from the four corners\n  // Skew the input space to determine which simplex cell we're in\n\n  var s = (xin + yin + zin) * F3; // Hairy factor for 2D\n\n  var i = Math.floor(xin + s);\n  var j = Math.floor(yin + s);\n  var k = Math.floor(zin + s);\n  var t = (i + j + k) * G3;\n  var x0 = xin - i + t; // The x,y distances from the cell origin, unskewed.\n\n  var y0 = yin - j + t;\n  var z0 = zin - k + t; // For the 3D case, the simplex shape is a slightly irregular tetrahedron.\n  // Determine which simplex we are in.\n\n  var i1, j1, k1; // Offsets for second corner of simplex in (i,j,k) coords\n\n  var i2, j2, k2; // Offsets for third corner of simplex in (i,j,k) coords\n\n  if (x0 >= y0) {\n    if (y0 >= z0) {\n      i1 = 1;\n      j1 = 0;\n      k1 = 0;\n      i2 = 1;\n      j2 = 1;\n      k2 = 0;\n    } else if (x0 >= z0) {\n      i1 = 1;\n      j1 = 0;\n      k1 = 0;\n      i2 = 1;\n      j2 = 0;\n      k2 = 1;\n    } else {\n      i1 = 0;\n      j1 = 0;\n      k1 = 1;\n      i2 = 1;\n      j2 = 0;\n      k2 = 1;\n    }\n  } else {\n    if (y0 < z0) {\n      i1 = 0;\n      j1 = 0;\n      k1 = 1;\n      i2 = 0;\n      j2 = 1;\n      k2 = 1;\n    } else if (x0 < z0) {\n      i1 = 0;\n      j1 = 1;\n      k1 = 0;\n      i2 = 0;\n      j2 = 1;\n      k2 = 1;\n    } else {\n      i1 = 0;\n      j1 = 1;\n      k1 = 0;\n      i2 = 1;\n      j2 = 1;\n      k2 = 0;\n    }\n  } // A step of (1,0,0) in (i,j,k) means a step of (1-c,-c,-c) in (x,y,z),\n  // a step of (0,1,0) in (i,j,k) means a step of (-c,1-c,-c) in (x,y,z), and\n  // a step of (0,0,1) in (i,j,k) means a step of (-c,-c,1-c) in (x,y,z), where\n  // c = 1/6.\n\n\n  var x1 = x0 - i1 + G3; // Offsets for second corner\n\n  var y1 = y0 - j1 + G3;\n  var z1 = z0 - k1 + G3;\n  var x2 = x0 - i2 + 2 * G3; // Offsets for third corner\n\n  var y2 = y0 - j2 + 2 * G3;\n  var z2 = z0 - k2 + 2 * G3;\n  var x3 = x0 - 1 + 3 * G3; // Offsets for fourth corner\n\n  var y3 = y0 - 1 + 3 * G3;\n  var z3 = z0 - 1 + 3 * G3; // Work out the hashed gradient indices of the four simplex corners\n\n  i &= 255;\n  j &= 255;\n  k &= 255;\n  var gi0 = gradP[i + perm[j + perm[k]]];\n  var gi1 = gradP[i + i1 + perm[j + j1 + perm[k + k1]]];\n  var gi2 = gradP[i + i2 + perm[j + j2 + perm[k + k2]]];\n  var gi3 = gradP[i + 1 + perm[j + 1 + perm[k + 1]]]; // Calculate the contribution from the four corners\n\n  var t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0;\n\n  if (t0 < 0) {\n    n0 = 0;\n  } else {\n    t0 *= t0;\n    n0 = t0 * t0 * gi0.dot3(x0, y0, z0); // (x,y) of grad3 used for 2D gradient\n  }\n\n  var t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1;\n\n  if (t1 < 0) {\n    n1 = 0;\n  } else {\n    t1 *= t1;\n    n1 = t1 * t1 * gi1.dot3(x1, y1, z1);\n  }\n\n  var t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2;\n\n  if (t2 < 0) {\n    n2 = 0;\n  } else {\n    t2 *= t2;\n    n2 = t2 * t2 * gi2.dot3(x2, y2, z2);\n  }\n\n  var t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3;\n\n  if (t3 < 0) {\n    n3 = 0;\n  } else {\n    t3 *= t3;\n    n3 = t3 * t3 * gi3.dot3(x3, y3, z3);\n  } // Add contributions from each corner to get the final noise value.\n  // The result is scaled to return values in the interval [-1,1].\n\n\n  return 32 * (n0 + n1 + n2 + n3);\n}; // ##### Perlin noise stuff\n// 2D Perlin Noise\n\nvar perlin2 = function perlin2(x, y) {\n  // Find unit grid cell containing point\n  var X = Math.floor(x),\n      Y = Math.floor(y); // Get relative xy coordinates of point within that cell\n\n  x = x - X;\n  y = y - Y; // Wrap the integer cells at 255 (smaller integer period can be introduced here)\n\n  X = X & 255;\n  Y = Y & 255; // Calculate noise contributions from each of the four corners\n\n  var n00 = gradP[X + perm[Y]].dot2(x, y);\n  var n01 = gradP[X + perm[Y + 1]].dot2(x, y - 1);\n  var n10 = gradP[X + 1 + perm[Y]].dot2(x - 1, y);\n  var n11 = gradP[X + 1 + perm[Y + 1]].dot2(x - 1, y - 1); // Compute the fade curve value for x\n\n  var u = fade(x); // Interpolate the four results\n\n  return lerp(lerp(n00, n10, u), lerp(n01, n11, u), fade(y));\n}; // 3D Perlin Noise\n\nvar perlin3 = function perlin3(x, y, z) {\n  // Find unit grid cell containing point\n  var X = Math.floor(x),\n      Y = Math.floor(y),\n      Z = Math.floor(z); // Get relative xyz coordinates of point within that cell\n\n  x = x - X;\n  y = y - Y;\n  z = z - Z; // Wrap the integer cells at 255 (smaller integer period can be introduced here)\n\n  X = X & 255;\n  Y = Y & 255;\n  Z = Z & 255; // Calculate noise contributions from each of the eight corners\n\n  var n000 = gradP[X + perm[Y + perm[Z]]].dot3(x, y, z);\n  var n001 = gradP[X + perm[Y + perm[Z + 1]]].dot3(x, y, z - 1);\n  var n010 = gradP[X + perm[Y + 1 + perm[Z]]].dot3(x, y - 1, z);\n  var n011 = gradP[X + perm[Y + 1 + perm[Z + 1]]].dot3(x, y - 1, z - 1);\n  var n100 = gradP[X + 1 + perm[Y + perm[Z]]].dot3(x - 1, y, z);\n  var n101 = gradP[X + 1 + perm[Y + perm[Z + 1]]].dot3(x - 1, y, z - 1);\n  var n110 = gradP[X + 1 + perm[Y + 1 + perm[Z]]].dot3(x - 1, y - 1, z);\n  var n111 = gradP[X + 1 + perm[Y + 1 + perm[Z + 1]]].dot3(x - 1, y - 1, z - 1); // Compute the fade curve value for x, y, z\n\n  var u = fade(x);\n  var v = fade(y);\n  var w = fade(z); // Interpolate\n\n  return lerp(lerp(lerp(n000, n100, u), lerp(n001, n101, u), w), lerp(lerp(n010, n110, u), lerp(n011, n111, u), w), v);\n};\n\nvar noise = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  seed: seed,\n  simplex2: simplex2,\n  simplex3: simplex3,\n  perlin2: perlin2,\n  perlin3: perlin3\n});\n\nvar TAU = Math.PI * 2;\nvar FlashGen = /*#__PURE__*/function () {\n  function FlashGen(props) {\n    _classCallCheck(this, FlashGen);\n\n    _defineProperty(this, \"nextBurstTime\", 0);\n\n    _defineProperty(this, \"nextFlashEndTime\", 0);\n\n    _defineProperty(this, \"flashesDone\", 0);\n\n    _defineProperty(this, \"isFlashing\", false);\n\n    _defineProperty(this, \"currentCount\", 0);\n\n    _defineProperty(this, \"flashIntensity\", 0);\n\n    _defineProperty(this, \"isDecaying\", false);\n\n    _defineProperty(this, \"autoBurst\", true);\n\n    _defineProperty(this, \"decaySpeed\", 40);\n\n    _defineProperty(this, \"minInterval\", 5000);\n\n    _defineProperty(this, \"maxInterval\", 10000);\n\n    _defineProperty(this, \"minDuration\", 50);\n\n    _defineProperty(this, \"maxDuration\", 300);\n\n    _defineProperty(this, \"count\", 5);\n\n    Object.assign(this, props);\n  }\n\n  _createClass(FlashGen, [{\n    key: \"scheduleNextBurst\",\n    value: function scheduleNextBurst(currentTime) {\n      var burstInterval = Math.random() * (this.maxInterval - this.minInterval) + this.minInterval;\n      this.nextBurstTime = currentTime + burstInterval / 1000;\n      this.flashesDone = 0;\n      this.isFlashing = false;\n    }\n  }, {\n    key: \"burst\",\n    value: function burst() {\n      this.nextBurstTime = 0;\n      this.flashesDone = 0;\n      this.isFlashing = false;\n    }\n  }, {\n    key: \"update\",\n    value: function update(currentTime, delta) {\n      if (currentTime > this.nextBurstTime && this.currentCount === 0) {\n        this.currentCount = Math.floor(Math.random() * this.count) + 1;\n      }\n\n      if (this.flashesDone < this.currentCount && currentTime > this.nextBurstTime) {\n        if (!this.isFlashing) {\n          this.isFlashing = true;\n          this.flashIntensity = 1;\n          var flashDuration = Math.random() * (this.maxDuration - this.minDuration) + this.minDuration;\n          this.nextFlashEndTime = currentTime + flashDuration / 1000;\n        } else if (this.isFlashing && currentTime > this.nextFlashEndTime) {\n          this.isFlashing = false;\n          this.isDecaying = true;\n          this.flashesDone++;\n\n          if (this.flashesDone >= this.currentCount) {\n            this.currentCount = 0;\n            if (this.autoBurst) this.scheduleNextBurst(currentTime);\n          }\n        }\n      }\n\n      if (this.isDecaying) {\n        this.flashIntensity -= delta * this.decaySpeed;\n        this.flashIntensity = Math.max(0, Math.min(1, this.flashIntensity));\n\n        if (this.flashIntensity <= 0) {\n          this.isDecaying = false;\n          this.flashIntensity = 0;\n        }\n      }\n\n      return this.flashIntensity;\n    }\n  }]);\n\n  return FlashGen;\n}(); // Credits @kchapelier https://github.com/kchapelier/wavefunctioncollapse/blob/master/example/lcg.js#L22-L30\n\nfunction normalizeSeed(seed) {\n  if (typeof seed === \"number\") {\n    seed = Math.abs(seed);\n  } else if (typeof seed === \"string\") {\n    var string = seed;\n    seed = 0;\n\n    for (var i = 0; i < string.length; i++) {\n      seed = (seed + (i + 1) * (string.charCodeAt(i) % 96)) % 2147483647;\n    }\n  }\n\n  if (seed === 0) {\n    seed = 311;\n  }\n\n  return seed;\n}\n\nfunction lcgRandom(seed) {\n  var state = normalizeSeed(seed);\n  return function () {\n    var result = state * 48271 % 2147483647;\n    state = result;\n    return result / 2147483647;\n  };\n}\n\nvar Generator = function Generator(_seed) {\n  var _this = this;\n\n  _classCallCheck(this, Generator);\n\n  _defineProperty(this, \"seed\", 0);\n\n  _defineProperty(this, \"init\", function (seed) {\n    _this.seed = seed;\n    _this.value = lcgRandom(seed);\n  });\n\n  _defineProperty(this, \"value\", lcgRandom(this.seed));\n\n  this.init(_seed);\n};\nvar defaultGen = new Generator(Math.random());\n/***\n * [3D] Sphere\n */\n\nvar defaultSphere = {\n  radius: 1,\n  center: [0, 0, 0]\n}; // random on surface of sphere\n// - https://twitter.com/fermatslibrary/status/1430932503578226688\n// - https://mathworld.wolfram.com/SpherePointPicking.html\n\nfunction onSphere(buffer, sphere) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultSphere$sphere = _objectSpread2(_objectSpread2({}, defaultSphere), sphere),\n      radius = _defaultSphere$sphere.radius,\n      center = _defaultSphere$sphere.center;\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    var u = rng.value();\n    var v = rng.value();\n    var theta = Math.acos(2 * v - 1);\n    var phi = TAU * u;\n    buffer[i] = Math.sin(theta) * Math.cos(phi) * radius + center[0];\n    buffer[i + 1] = Math.sin(theta) * Math.sin(phi) * radius + center[1];\n    buffer[i + 2] = Math.cos(theta) * radius + center[2];\n  }\n\n  return buffer;\n} // from \"Another Method\" https://datagenetics.com/blog/january32020/index.html\n\nfunction inSphere(buffer, sphere) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultSphere$sphere2 = _objectSpread2(_objectSpread2({}, defaultSphere), sphere),\n      radius = _defaultSphere$sphere2.radius,\n      center = _defaultSphere$sphere2.center;\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    var u = Math.pow(rng.value(), 1 / 3);\n    var x = rng.value() * 2 - 1;\n    var y = rng.value() * 2 - 1;\n    var z = rng.value() * 2 - 1;\n    var mag = Math.sqrt(x * x + y * y + z * z);\n    x = u * x / mag;\n    y = u * y / mag;\n    z = u * z / mag;\n    buffer[i] = x * radius + center[0];\n    buffer[i + 1] = y * radius + center[1];\n    buffer[i + 2] = z * radius + center[2];\n  }\n\n  return buffer;\n}\n/***\n * [2D] Circle\n */\n\nvar defaultCircle = {\n  radius: 1,\n  center: [0, 0]\n}; // random circle https://stackoverflow.com/a/50746409\n\nfunction inCircle(buffer, circle) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultCircle$circle = _objectSpread2(_objectSpread2({}, defaultCircle), circle),\n      radius = _defaultCircle$circle.radius,\n      center = _defaultCircle$circle.center;\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    var r = radius * Math.sqrt(rng.value());\n    var theta = rng.value() * TAU;\n    buffer[i] = Math.sin(theta) * r + center[0];\n    buffer[i + 1] = Math.cos(theta) * r + center[1];\n  }\n\n  return buffer;\n}\nfunction onCircle(buffer, circle) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultCircle$circle2 = _objectSpread2(_objectSpread2({}, defaultCircle), circle),\n      radius = _defaultCircle$circle2.radius,\n      center = _defaultCircle$circle2.center;\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    var theta = rng.value() * TAU;\n    buffer[i] = Math.sin(theta) * radius + center[0];\n    buffer[i + 1] = Math.cos(theta) * radius + center[1];\n  }\n\n  return buffer;\n}\n/**\n * [2D] Plane\n */\n\nvar defaultRect = {\n  sides: 1,\n  center: [0, 0]\n};\nfunction inRect(buffer, rect) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultRect$rect = _objectSpread2(_objectSpread2({}, defaultRect), rect),\n      sides = _defaultRect$rect.sides,\n      center = _defaultRect$rect.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n  }\n\n  return buffer;\n}\nfunction onRect(buffer, rect) {\n  return buffer;\n}\n/***\n * [3D] Box\n */\n\nfunction inBox(buffer, box) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultBox$box = _objectSpread2(_objectSpread2({}, defaultBox), box),\n      sides = _defaultBox$box.sides,\n      center = _defaultBox$box.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  var sideZ = typeof sides === \"number\" ? sides : sides[2];\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n    buffer[i + 2] = (rng.value() - 0.5) * sideZ + center[2];\n  }\n\n  return buffer;\n}\nvar defaultBox = {\n  sides: 1,\n  center: [0, 0, 0]\n};\nfunction onBox(buffer, box) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultBox$box2 = _objectSpread2(_objectSpread2({}, defaultBox), box),\n      sides = _defaultBox$box2.sides,\n      center = _defaultBox$box2.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  var sideZ = typeof sides === \"number\" ? sides : sides[2];\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n    buffer[i + 2] = (rng.value() - 0.5) * sideZ + center[2];\n  }\n\n  return buffer;\n}\n\nvar index = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  FlashGen: FlashGen,\n  Generator: Generator,\n  onSphere: onSphere,\n  inSphere: inSphere,\n  inCircle: inCircle,\n  onCircle: onCircle,\n  inRect: inRect,\n  onRect: onRect,\n  inBox: inBox,\n  onBox: onBox,\n  noise: noise\n});\n\nexport { FlashGen as F, Generator as G, inSphere as a, inCircle as b, onCircle as c, inRect as d, onRect as e, inBox as f, onBox as g, index as i, noise as n, onSphere as o };\n"], "mappings": ";;;;;;;;AAAA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAE7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AAEjD,QAAI,gBAAgB;AAClB,gBAAU,QAAQ,OAAO,SAAU,KAAK;AACtC,eAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,MACtD,CAAC;AAAA,IACH;AAEA,SAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EAC/B;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAEpD,QAAI,IAAI,GAAG;AACT,cAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AACnD,wBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MAC1C,CAAC;AAAA,IACH,WAAW,OAAO,2BAA2B;AAC3C,aAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,IAC1E,OAAO;AACL,cAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAC7C,eAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,MACjF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AACT;;;ACnDA,SAAS,gBAAgB,UAAU,aAAa;AAC9C,MAAI,EAAE,oBAAoB,cAAc;AACtC,UAAM,IAAI,UAAU,mCAAmC;AAAA,EACzD;AACF;;;ACJA,SAAS,gBAAgB,GAAGA,IAAG;AAC7B,oBAAkB,OAAO,kBAAkB,SAASC,iBAAgBC,IAAGF,IAAG;AACxE,IAAAE,GAAE,YAAYF;AACd,WAAOE;AAAA,EACT;AAEA,SAAO,gBAAgB,GAAGF,EAAC;AAC7B;AAEA,SAAS,4BAA4B;AACnC,MAAI,OAAO,YAAY,eAAe,CAAC,QAAQ,UAAW,QAAO;AACjE,MAAI,QAAQ,UAAU,KAAM,QAAO;AACnC,MAAI,OAAO,UAAU,WAAY,QAAO;AAExC,MAAI;AACF,YAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAC,GAAG,WAAY;AAAA,IAAC,CAAC,CAAC;AAC7E,WAAO;AAAA,EACT,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;;;ACRA,SAAS,eAAe;AACtB,WAAS,OAAO,UAAU,QAAQ,QAAQ,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACxF,UAAM,IAAI,IAAI,UAAU,IAAI;AAAA,EAC9B;AAEA,MAAI,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC;AACf,SAAO,IAAI,IAAI,IAAI;AACrB;AAYA,SAAS,eAAe;AACtB,WAAS,QAAQ,UAAU,QAAQ,QAAQ,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC9F,UAAM,KAAK,IAAI,UAAU,KAAK;AAAA,EAChC;AAEA,MAAI,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC;AACf,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC7E;AAYA,SAAS,eAAe;AACtB,WAAS,QAAQ,UAAU,QAAQ,QAAQ,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC9F,UAAM,KAAK,IAAI,UAAU,KAAK;AAAA,EAChC;AAEA,QAAM,CAAC;AACH,QAAM,CAAC;AACP,QAAM,CAAC;AACP,QAAM,CAAC;AACP,QAAM,CAAC;AACP,QAAM,CAAC;AACP,QAAM,CAAC;AACP,QAAM,CAAC;AACP,QAAM,CAAC;AACP,QAAM,CAAC;AACP,QAAM,EAAE;AACR,QAAM,EAAE;AACR,QAAM,EAAE;AACR,QAAM,EAAE;AACR,QAAM,EAAE;AACd;AAqBA,SAAS,SAASG,SAAQ,GAAG,GAAG;AAC9B,MAAI,mBAAmBA,QAAO,MAAM,EAAE,UAAU;AAEhD,MAAI,IAAI,CAAC;AACT,MAAI,IAAI,iBAAiB,SAAS;AAClC,MAAI,IAAI,KAAK,KAAK,CAAC;AAEnB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,QAAI,UAAU,iBAAiB,SAAS,CAAC;AACzC,QAAI,MAAM,KAAK,MAAM,IAAI,CAAC;AAC1B,QAAI,MAAM,IAAI;AAEd,QAAI,QAAQ,IAAI,KAAK,QAAQ,IAAI,GAAG;AAClC,QAAE,KAAK,OAAO;AAAA,IAChB;AAAA,EACF;AAEA,SAAO,aAAa,MAAM,QAAQ,CAAC;AACrC;AAKA,SAAS,WAAW,IAAI,IAAI;AAC1B,MAAI,MAAM,CAAC;AACX,MAAI,UAAU,GAAG,QAAQ;AACzB,MAAI,UAAU,GAAG,QAAQ;AAEzB,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,QAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,EACjC;AAEA,SAAO,IAAI,QAAQ,EAAE,UAAU,GAAG;AACpC;AAEA,IAAI,SAAsB,OAAO,OAAO;AAAA,EACtC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;AC5ID,SAAS,gBAAgB,KAAK;AAC5B,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO;AACjC;AAEA,SAAS,sBAAsB,KAAK,GAAG;AACrC,MAAI,KAAK,OAAO,OAAO,OAAO,OAAO,WAAW,eAAe,IAAI,OAAO,QAAQ,KAAK,IAAI,YAAY;AAEvG,MAAI,MAAM,KAAM;AAChB,MAAI,OAAO,CAAC;AACZ,MAAI,KAAK;AACT,MAAI,KAAK;AAET,MAAI,IAAI;AAER,MAAI;AACF,SAAK,KAAK,GAAG,KAAK,GAAG,GAAG,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,MAAM;AAChE,WAAK,KAAK,GAAG,KAAK;AAElB,UAAI,KAAK,KAAK,WAAW,EAAG;AAAA,IAC9B;AAAA,EACF,SAAS,KAAK;AACZ,SAAK;AACL,SAAK;AAAA,EACP,UAAE;AACA,QAAI;AACF,UAAI,CAAC,MAAM,GAAG,QAAQ,KAAK,KAAM,IAAG,QAAQ,EAAE;AAAA,IAChD,UAAE;AACA,UAAI,GAAI,OAAM;AAAA,IAChB;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAE/C,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AAEpE,SAAO;AACT;AAEA,SAAS,4BAA4B,GAAG,QAAQ;AAC9C,MAAI,CAAC,EAAG;AACR,MAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAC7D,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AACvD,MAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AACnD,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AACjH;AAEA,SAAS,mBAAmB;AAC1B,QAAM,IAAI,UAAU,2IAA2I;AACjK;AAEA,SAAS,eAAe,KAAK,GAAG;AAC9B,SAAO,gBAAgB,GAAG,KAAK,sBAAsB,KAAK,CAAC,KAAK,4BAA4B,KAAK,CAAC,KAAK,iBAAiB;AAC1H;AAEA,SAAS,mBAAmB,KAAK;AAC/B,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO,kBAAkB,GAAG;AACtD;AAEA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,OAAO,WAAW,eAAe,KAAK,OAAO,QAAQ,KAAK,QAAQ,KAAK,YAAY,KAAK,KAAM,QAAO,MAAM,KAAK,IAAI;AAC1H;AAEA,SAAS,qBAAqB;AAC5B,QAAM,IAAI,UAAU,sIAAsI;AAC5J;AAEA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,4BAA4B,GAAG,KAAK,mBAAmB;AACpH;AAEA,SAAS,WAAW,QAAQ,MAAM,OAAO;AACvC,MAAI,0BAA0B,GAAG;AAC/B,iBAAa,QAAQ;AAAA,EACvB,OAAO;AACL,iBAAa,SAASC,YAAWC,SAAQC,OAAMC,QAAO;AACpD,UAAI,IAAI,CAAC,IAAI;AACb,QAAE,KAAK,MAAM,GAAGD,KAAI;AACpB,UAAI,cAAc,SAAS,KAAK,MAAMD,SAAQ,CAAC;AAC/C,UAAI,WAAW,IAAI,YAAY;AAC/B,UAAIE,OAAO,iBAAgB,UAAUA,OAAM,SAAS;AACpD,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO,WAAW,MAAM,MAAM,SAAS;AACzC;AAYA,SAAS,kBAAkB,OAAOC,WAAU;AAC1C,MAAI,aAAa,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC1C,KAAK,WAAW,CAAC,GACjB,KAAK,WAAW,CAAC;AAErB,MAAI,cAAc,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC3C,KAAK,YAAY,CAAC,GAClB,KAAK,YAAY,CAAC;AAEtB,MAAI,cAAc,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC3C,KAAK,YAAY,CAAC,GAClB,KAAK,YAAY,CAAC;AAEtB,MAAI,SAAS,eAAe,OAAO,CAAC,GAChC,KAAK,OAAO,CAAC,GACb,KAAK,OAAO,CAAC;AAGjB,MAAIC,UAAS,IAAI,QAAQ;AAEzB,EAAAA,QAAO,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,CAAC;AACjI,SAAOA,QAAO,YAAY,KAAK;AACjC;AACA,SAAS,oBAAoBD,WAAU;AACrC,MAAI,cAAc,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC3C,KAAK,YAAY,CAAC,GAClB,KAAK,YAAY,CAAC;AAEtB,MAAI,cAAc,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC3C,KAAK,YAAY,CAAC,GAClB,KAAK,YAAY,CAAC;AAEtB,MAAI,cAAc,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC3C,KAAK,YAAY,CAAC,GAClB,KAAK,YAAY,CAAC;AAGtB,SAAO,aAAa,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC;AACrD;AAeA,SAAS,mBAAmB,QAAQ;AAClC,SAAO,oBAAoB,MAAM,MAAM;AACzC;AAEA,SAAS,oBAAoBA,WAAU;AACrC,SAAO,oBAAoBA,SAAQ,IAAI;AACzC;AA+BA,SAAS,gBAAgBA,WAAU;AAGjC,MAAI,cAAc,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC3C,KAAK,YAAY,CAAC,GAClB,KAAK,YAAY,CAAC;AAGtB,MAAI,cAAc,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC3C,KAAK,YAAY,CAAC,GAClB,KAAK,YAAY,CAAC;AAGtB,MAAI,cAAc,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC3C,KAAK,YAAY,CAAC,GAClB,KAAK,YAAY,CAAC;AAEtB,MAAI,mBAAmBA,SAAQ,EAAG,QAAO;AAEzC,MAAI,IAAI,IAAI,QAAQ;AAEpB,IAAE,IAAI,GAAG,GAAG,GAAG,GAAG,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,GAAG,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,GAAG,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAC1G,MAAI,MAAM,SAAS,GAAG,GAAG,CAAC;AAC1B,MAAI,MAAM,SAAS,GAAG,GAAG,CAAC;AAC1B,MAAI,MAAM,SAAS,GAAG,GAAG,CAAC;AAC1B,MAAI,MAAM,SAAS,GAAG,GAAG,CAAC;AAC1B,MAAI,KAAK,OAAO,MAAM;AACtB,MAAI,KAAK,OAAO,MAAM;AACtB,MAAI,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM;AACnC,SAAO;AAAA,IACL,GAAG,KAAK,IAAI,EAAE,MAAM,IAAI,IAAI;AAAA,IAC5B,GAAG,KAAK,IAAI,EAAE,MAAM,IAAI,IAAI,CAAC;AAAA,IAC7B,GAAG,KAAK,KAAK,EAAE;AAAA,EACjB;AACF;AAEA,SAAS,sBAAsB,OAAOA,WAAU;AAC9C,MAAI,OAAO,MAAM,QAAQA,UAAS,CAAC,CAAC,IAAIA,UAAS,CAAC,IAAIA,UAAS,CAAC,EAAE,QAAQ,GACtE,QAAQ,eAAe,MAAM,CAAC,GAC9B,KAAK,MAAM,CAAC,GACZ,KAAK,MAAM,CAAC;AAEhB,MAAI,QAAQ,MAAM,QAAQA,UAAS,CAAC,CAAC,IAAIA,UAAS,CAAC,IAAIA,UAAS,CAAC,EAAE,QAAQ,GACvE,QAAQ,eAAe,OAAO,CAAC,GAC/B,KAAK,MAAM,CAAC,GACZ,KAAK,MAAM,CAAC;AAEhB,MAAI,QAAQ,MAAM,QAAQA,UAAS,CAAC,CAAC,IAAIA,UAAS,CAAC,IAAIA,UAAS,CAAC,EAAE,QAAQ,GACvE,QAAQ,eAAe,OAAO,CAAC,GAC/B,KAAK,MAAM,CAAC,GACZ,KAAK,MAAM,CAAC;AAEhB,MAAI,UAAU,eAAe,OAAO,CAAC,GACjC,KAAK,QAAQ,CAAC,GACd,KAAK,QAAQ,CAAC;AAElB,MAAI,mBAAmBA,SAAQ,EAAG,OAAM,IAAI,MAAM,wCAAwC;AAO1F,MAAI,QAAQ,KAAK;AACjB,MAAI,QAAQ,KAAK;AACjB,MAAI,QAAQ,KAAK;AACjB,MAAI,QAAQ,KAAK;AACjB,MAAI,QAAQ,KAAK;AACjB,MAAI,QAAQ,KAAK;AAEjB,MAAI,IAAI,aAAa,OAAO,OAAO,QAAQ,QAAQ,QAAQ,OAAO,OAAO,OAAO,QAAQ,QAAQ,QAAQ,OAAO,OAAO,OAAO,QAAQ,QAAQ,QAAQ,KAAK;AAE1J,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AAEA,SAAO,CAAC,oBAAoBA,SAAQ,IAAI,IAAI,IAAI,IAAI;AACtD;AAEA,IAAI,MAAM,IAAI,QAAQ;AACtB,IAAI,MAAM,IAAI,QAAQ;AAYtB,SAAS,wBAAwB,QAAQ;AACvC,MAAI,cAAc,OAAO,IAAI,SAAUE,IAAG;AACxC,QAAI,MAAM,QAAQA,EAAC,GAAG;AACpB,aAAO,WAAW,SAAS,mBAAmBA,EAAC,CAAC;AAAA,IAClD;AAEA,WAAOA;AAAA,EACT,CAAC,GACG,eAAe,eAAe,aAAa,CAAC,GAC5C,KAAK,aAAa,CAAC,GACnB,KAAK,aAAa,CAAC,GACnB,KAAK,aAAa,CAAC;AAEvB,MAAI,mBAAmB,MAAM,EAAG,QAAO;AAEvC,MAAI,OAAO,IAAI,WAAW,IAAI,EAAE;AAEhC,MAAI,OAAO,IAAI,WAAW,IAAI,EAAE;AAChC,MAAI,QAAQ,KAAK,MAAM,IAAI;AAC3B,SAAO,QAAQ;AACjB;AAEA,IAAI,WAAwB,OAAO,OAAO;AAAA,EACxC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;ACxTD,SAAS,MAAM,OAAO,KAAK,KAAK;AAC9B,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC;AAC3C;AAEA,SAAS,OAAO,GAAG,QAAQ;AACzB,SAAO,MAAM,IAAI,KAAK,MAAM,IAAI,MAAM,IAAI,QAAQ,GAAG,MAAM;AAC7D;AAEA,SAAS,WAAW,SAAS,QAAQ;AACnC,MAAI,QAAQ,OAAO,SAAS,SAAS,KAAK,KAAK,CAAC;AAChD,MAAI,QAAQ,KAAK,GAAI,UAAS,KAAK,KAAK;AACxC,SAAO;AACT;AAKA,SAAS,SAAS,SAAS;AACzB,SAAO,UAAU,MAAM,KAAK;AAC9B;AAKA,SAAS,SAAS,SAAS;AACzB,SAAO,UAAU,MAAM,KAAK;AAC9B;AAEA,SAAS,kBAAkB,QAAQ,MAAM;AACvC,MAAI,cAAc,KAAK,QACnB,SAAS,gBAAgB,SAAS,IAAI;AAC1C,MAAI,UAAU,OAAO,SAAS;AAC9B,MAAI,SAAS,IAAI;AACjB,MAAI,YAAY,KAAK,MAAM,IAAI;AAE/B,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,QAAI,IAAI,IAAI,SAAS,IAAI,SAAS;AAClC,QAAI,WAAW,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC;AAC3C,QAAI,MAAM,IAAI,UAAU;AACxB,QAAI,IAAI,KAAK,IAAI,GAAG,IAAI;AACxB,QAAI,IAAI,KAAK,IAAI,GAAG,IAAI;AACxB,WAAO,CAAC,IAAI,IAAI;AAChB,WAAO,IAAI,CAAC,IAAI,IAAI;AACpB,WAAO,IAAI,CAAC,IAAI,IAAI;AAAA,EACtB;AACF;AAEA,SAAS,aAAa,GAAG,GAAG;AAC1B,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,OAAO;AACrF,SAAO,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,OAAO,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,OAAO,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI;AACzF;AASA,SAAS,cAAc,GAAG,GAAG;AAC3B,MAAI,EAAE,MAAM,EAAE,GAAG;AAGf,QAAI,OAAO,EAAE,MAAM,aAAa;AAC9B,UAAI,EAAE,MAAM,EAAE,GAAG;AACf,eAAO,EAAE,IAAI,EAAE;AAAA,MACjB;AAAA,IACF;AAEA,WAAO,EAAE,IAAI,EAAE;AAAA,EACjB;AAEA,SAAO,EAAE,IAAI,EAAE;AACjB;AAWA,SAAS,WAAW,SAAS;AAC3B,MAAI,SAAS,QAAQ,KAAK,aAAa;AAGvC,MAAI,SAAS,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAElC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,WAAO,KAAK,OAAO,CAAC,CAAC;AAErB,WAAO,OAAO,SAAS,KAAK,wBAAwB,mBAAmB,OAAO,MAAM,EAAE,CAAC,CAAC,GAAG;AAEzF,aAAO,OAAO,OAAO,SAAS,GAAG,CAAC;AAAA,IACpC;AAAA,EACF;AAGA,MAAI,SAAS,CAAC,OAAO,OAAO,SAAS,CAAC,GAAG,OAAO,OAAO,SAAS,CAAC,CAAC;AAElE,WAAS,KAAK,OAAO,SAAS,GAAG,MAAM,GAAG,MAAM;AAE9C,WAAO,KAAK,OAAO,EAAE,CAAC;AAEtB,WAAO,OAAO,SAAS,KAAK,wBAAwB,mBAAmB,OAAO,MAAM,EAAE,CAAC,CAAC,GAAG;AAEzF,aAAO,OAAO,OAAO,SAAS,GAAG,CAAC;AAAA,IACpC;AAAA,EACF;AAGA,SAAO,OAAO,GAAG,CAAC;AAClB,SAAO,OAAO,OAAO,SAAS,GAAG,CAAC;AAElC,MAAI,IAAI,CAAC,EAAE,OAAO,QAAQ,MAAM;AAChC,SAAO;AACT;AACA,SAAS,MAAM,GAAG,OAAO,OAAO;AAC9B,MAAI,QAAQ,eAAe,OAAO,CAAC,GAC/B,OAAO,MAAM,CAAC,GACd,QAAQ,MAAM,CAAC;AAEnB,MAAI,QAAQ,eAAe,OAAO,CAAC,GAC/B,OAAO,MAAM,CAAC,GACd,QAAQ,MAAM,CAAC;AAEnB,SAAO,QAAQ,IAAI,SAAS,QAAQ,SAAS,QAAQ;AACvD;AAWA,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,MAAM;AACzC;AAWA,SAAS,KAAK,IAAI,IAAI,GAAG;AACvB,SAAO,MAAM,IAAI,KAAK,KAAK;AAC7B;AAWA,SAAS,YAAY,IAAI,IAAI,GAAG;AAC9B,UAAQ,IAAI,OAAO,KAAK;AAC1B;AAKA,SAAS,UAAU,GAAG,GAAG,GAAG;AAC1B,MAAI,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC;AACvC,SAAO,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAC7B;AAKA,SAAS,2BAA2B,GAAG,GAAG,GAAG;AAC3C,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,CAAC;AACtD,MAAI,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,CAAC;AACtD,MAAI,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,CAAC;AACtD,SAAO,CAAC,IAAI,IAAI,EAAE;AACpB;AAOA,SAAS,qBAAqB,GAAG,GAAG;AAClC,MAAI,IAAI,IAAI,QAAQ,EAAE,aAAa,GAAG,CAAC;AACvC,MAAI,IAAI,EAAE,IAAI,CAAC;AACf,MAAI,IAAI,IAAI,QAAQ,EAAE,SAAS;AAG/B,MAAI,KAAK,IAAI,QAAQ,EAAE,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AACnE,MAAI,YAAY,IAAI,QAAQ,EAAE,iBAAiB,IAAI,EAAE,EAAE,eAAe,KAAK,IAAI,EAAE;AAEjF,MAAI,SAAS,WAAW,WAAW,GAAG,EAAE,GAAG,SAAS;AAEpD,SAAO;AACT;AAEA,SAAS,kBAAkB,GAAG,GAAG,GAAG;AAClC,MAAI,MAAM,KAAK,KAAK,CAAC;AACrB,MAAI,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;AAC1B,SAAO,CAAC,KAAK,GAAG;AAClB;AAEA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,IAAI,KAAK,IAAI,GAAG,IAAI;AACxB,MAAI,IAAI,CAAC,KAAK,IAAI,GAAG,IAAI;AACzB,SAAO,CAAC,GAAG,GAAG,CAAC;AACjB;AAKA,SAAS,yBAAyB,OAAO,SAAS;AAChD,MAAI,WAAW,eAAe,SAAS,CAAC,GACpC,IAAI,SAAS,CAAC,GACd,IAAI,SAAS,CAAC;AAElB,MAAIC,UAAS,qBAAqB,MAAM,QAAQ,IAAI,QAAQ,GAAG,GAAG,CAAC,CAAC;AACpE,MAAI,IAAI,YAAY,EAAE,MAAM,EAAE,aAAaA,OAAM,EAAE,GAAG,EAAE,MAAM,EAAE,aAAaA,OAAM,EAAE,GAAG,CAAC;AACzF,SAAO,IAAI,QAAQ,EAAE,YAAY,GAAG,GAAG,CAAC;AAC1C;AAKA,SAAS,qBAAqBC,IAAG,OAAO;AACtC,MAAI,IAAI,MAAM,OAAO,IAAIA,EAAC;AAE1B,SAAO;AACT;AACA,SAAS,eAAe,QAAQ,OAAO;AACrC,MAAI,UAAU,eAAe,QAAQ,CAAC,GAClC,KAAK,QAAQ,CAAC,GACd,KAAK,QAAQ,CAAC,GACd,KAAK,QAAQ,CAAC;AAElB,MAAI,SAAS,eAAe,OAAO,CAAC,GAChC,KAAK,OAAO,CAAC,GACb,KAAK,OAAO,CAAC;AAEjB,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK;AAClC;AACA,SAAS,eAAeC,QAAO,MAAM;AACnC,MAAI,QAAQ,eAAe,MAAM,CAAC,GAC9B,KAAK,MAAM,CAAC,GACZ,KAAK,MAAM,CAAC;AAEhB,MAAI,IAAI,KAAK;AACb,MAAI,IAAIA,SAAQ;AAChB,MAAI,IAAIA,SAAQ,IAAI;AACpB,MAAI,IAAI,IAAI;AACZ,MAAI,IAAI,IAAI;AACZ,SAAO,CAAC,GAAG,GAAG,CAAC;AACjB;AACA,SAAS,eAAe,QAAQ,MAAM;AACpC,SAAO,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,OAAO,CAAC;AACvC;AACA,SAAS,eAAeA,QAAO,SAAS;AACtC,MAAI,IAAIA,SAAQ;AAChB,MAAI,IAAI,KAAK,MAAMA,SAAQ,OAAO;AAClC,SAAO,CAAC,GAAG,CAAC;AACd;AAEA,IAAI,OAAoB,OAAO,OAAO;AAAA,EACpC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;ACnTD,SAAS,kBAAkB,QAAQ,OAAO;AACxC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,aAAa,MAAM,CAAC;AACxB,eAAW,aAAa,WAAW,cAAc;AACjD,eAAW,eAAe;AAC1B,QAAI,WAAW,WAAY,YAAW,WAAW;AACjD,WAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,EAC1D;AACF;AAEA,SAAS,aAAa,aAAa,YAAY,aAAa;AAC1D,MAAI,WAAY,mBAAkB,YAAY,WAAW,UAAU;AACnE,MAAI,YAAa,mBAAkB,aAAa,WAAW;AAC3D,SAAO;AACT;AAkBA,IAAI,OAAO,SAASC,MAAK,GAAG,GAAG,GAAG;AAChC,MAAI,QAAQ;AAEZ,kBAAgB,MAAMA,KAAI;AAE1B,kBAAgB,MAAM,QAAQ,SAAUC,IAAGC,IAAG;AAC5C,WAAO,MAAM,IAAID,KAAI,MAAM,IAAIC;AAAA,EACjC,CAAC;AAED,kBAAgB,MAAM,QAAQ,SAAUD,IAAGC,IAAGC,IAAG;AAC/C,WAAO,MAAM,IAAIF,KAAI,MAAM,IAAIC,KAAI,MAAM,IAAIC;AAAA,EAC/C,CAAC;AAED,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACX;AAEA,IAAI,QAAQ,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,KAAK,GAAG,GAAG,EAAE,GAAG,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,KAAK,GAAG,GAAG,EAAE,GAAG,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC;AAC3P,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,GAAG;AAEzpC,IAAI,OAAO,IAAI,MAAM,GAAG;AACxB,IAAI,QAAQ,IAAI,MAAM,GAAG;AAGzB,IAAI,OAAO,SAASC,MAAK,OAAO;AAC9B,MAAI,QAAQ,KAAK,QAAQ,GAAG;AAE1B,aAAS;AAAA,EACX;AAEA,UAAQ,KAAK,MAAM,KAAK;AAExB,MAAI,QAAQ,KAAK;AACf,aAAS,SAAS;AAAA,EACpB;AAEA,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,QAAI;AAEJ,QAAI,IAAI,GAAG;AACT,UAAI,EAAE,CAAC,IAAI,QAAQ;AAAA,IACrB,OAAO;AACL,UAAI,EAAE,CAAC,IAAI,SAAS,IAAI;AAAA,IAC1B;AAEA,SAAK,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI;AAC1B,UAAM,CAAC,IAAI,MAAM,IAAI,GAAG,IAAI,MAAM,IAAI,EAAE;AAAA,EAC1C;AACF;AACA,KAAK,CAAC;AAQN,IAAI,KAAK,OAAO,KAAK,KAAK,CAAC,IAAI;AAC/B,IAAI,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK;AAC9B,IAAI,KAAK,IAAI;AACb,IAAI,KAAK,IAAI;AAEb,IAAI,WAAW,SAASC,UAAS,KAAK,KAAK;AACzC,MAAI,IAAI,IAAI;AAGZ,MAAI,KAAK,MAAM,OAAO;AAEtB,MAAI,IAAI,KAAK,MAAM,MAAM,CAAC;AAC1B,MAAI,IAAI,KAAK,MAAM,MAAM,CAAC;AAC1B,MAAI,KAAK,IAAI,KAAK;AAClB,MAAI,KAAK,MAAM,IAAI;AAEnB,MAAI,KAAK,MAAM,IAAI;AAGnB,MAAI,IAAI;AAER,MAAI,KAAK,IAAI;AAEX,SAAK;AACL,SAAK;AAAA,EACP,OAAO;AAEL,SAAK;AACL,SAAK;AAAA,EACP;AAKA,MAAI,KAAK,KAAK,KAAK;AAEnB,MAAI,KAAK,KAAK,KAAK;AACnB,MAAI,KAAK,KAAK,IAAI,IAAI;AAEtB,MAAI,KAAK,KAAK,IAAI,IAAI;AAEtB,OAAK;AACL,OAAK;AACL,MAAI,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC;AAC3B,MAAI,MAAM,MAAM,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;AACrC,MAAI,MAAM,MAAM,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC;AAEnC,MAAI,KAAK,MAAM,KAAK,KAAK,KAAK;AAE9B,MAAI,KAAK,GAAG;AACV,SAAK;AAAA,EACP,OAAO;AACL,UAAM;AACN,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE;AAAA,EAChC;AAEA,MAAI,KAAK,MAAM,KAAK,KAAK,KAAK;AAE9B,MAAI,KAAK,GAAG;AACV,SAAK;AAAA,EACP,OAAO;AACL,UAAM;AACN,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE;AAAA,EAChC;AAEA,MAAI,KAAK,MAAM,KAAK,KAAK,KAAK;AAE9B,MAAI,KAAK,GAAG;AACV,SAAK;AAAA,EACP,OAAO;AACL,UAAM;AACN,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE;AAAA,EAChC;AAIA,SAAO,MAAM,KAAK,KAAK;AACzB;AAEA,IAAI,WAAW,SAASC,UAAS,KAAK,KAAK,KAAK;AAC9C,MAAI,IAAI,IAAI,IAAI;AAGhB,MAAI,KAAK,MAAM,MAAM,OAAO;AAE5B,MAAI,IAAI,KAAK,MAAM,MAAM,CAAC;AAC1B,MAAI,IAAI,KAAK,MAAM,MAAM,CAAC;AAC1B,MAAI,IAAI,KAAK,MAAM,MAAM,CAAC;AAC1B,MAAI,KAAK,IAAI,IAAI,KAAK;AACtB,MAAI,KAAK,MAAM,IAAI;AAEnB,MAAI,KAAK,MAAM,IAAI;AACnB,MAAI,KAAK,MAAM,IAAI;AAGnB,MAAI,IAAI,IAAI;AAEZ,MAAI,IAAI,IAAI;AAEZ,MAAI,MAAM,IAAI;AACZ,QAAI,MAAM,IAAI;AACZ,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AAAA,IACP,WAAW,MAAM,IAAI;AACnB,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AAAA,IACP,OAAO;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AAAA,IACP;AAAA,EACF,OAAO;AACL,QAAI,KAAK,IAAI;AACX,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AAAA,IACP,WAAW,KAAK,IAAI;AAClB,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AAAA,IACP,OAAO;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AAAA,IACP;AAAA,EACF;AAMA,MAAI,KAAK,KAAK,KAAK;AAEnB,MAAI,KAAK,KAAK,KAAK;AACnB,MAAI,KAAK,KAAK,KAAK;AACnB,MAAI,KAAK,KAAK,KAAK,IAAI;AAEvB,MAAI,KAAK,KAAK,KAAK,IAAI;AACvB,MAAI,KAAK,KAAK,KAAK,IAAI;AACvB,MAAI,KAAK,KAAK,IAAI,IAAI;AAEtB,MAAI,KAAK,KAAK,IAAI,IAAI;AACtB,MAAI,KAAK,KAAK,IAAI,IAAI;AAEtB,OAAK;AACL,OAAK;AACL,OAAK;AACL,MAAI,MAAM,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC;AACrC,MAAI,MAAM,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC,CAAC;AACpD,MAAI,MAAM,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC,CAAC;AACpD,MAAI,MAAM,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC;AAEjD,MAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAExC,MAAI,KAAK,GAAG;AACV,SAAK;AAAA,EACP,OAAO;AACL,UAAM;AACN,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE;AAAA,EACpC;AAEA,MAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAExC,MAAI,KAAK,GAAG;AACV,SAAK;AAAA,EACP,OAAO;AACL,UAAM;AACN,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE;AAAA,EACpC;AAEA,MAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAExC,MAAI,KAAK,GAAG;AACV,SAAK;AAAA,EACP,OAAO;AACL,UAAM;AACN,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE;AAAA,EACpC;AAEA,MAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAExC,MAAI,KAAK,GAAG;AACV,SAAK;AAAA,EACP,OAAO;AACL,UAAM;AACN,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE;AAAA,EACpC;AAIA,SAAO,MAAM,KAAK,KAAK,KAAK;AAC9B;AAGA,IAAI,UAAU,SAASC,SAAQ,GAAG,GAAG;AAEnC,MAAI,IAAI,KAAK,MAAM,CAAC,GAChB,IAAI,KAAK,MAAM,CAAC;AAEpB,MAAI,IAAI;AACR,MAAI,IAAI;AAER,MAAI,IAAI;AACR,MAAI,IAAI;AAER,MAAI,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC;AACtC,MAAI,MAAM,MAAM,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC;AAC9C,MAAI,MAAM,MAAM,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,KAAK,IAAI,GAAG,CAAC;AAC9C,MAAI,MAAM,MAAM,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,GAAG,IAAI,CAAC;AAEtD,MAAI,IAAI,KAAK,CAAC;AAEd,SAAO,KAAK,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAC3D;AAEA,IAAI,UAAU,SAASC,SAAQ,GAAG,GAAG,GAAG;AAEtC,MAAI,IAAI,KAAK,MAAM,CAAC,GAChB,IAAI,KAAK,MAAM,CAAC,GAChB,IAAI,KAAK,MAAM,CAAC;AAEpB,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AAER,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AAER,MAAI,OAAO,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC;AACpD,MAAI,OAAO,MAAM,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;AAC5D,MAAI,OAAO,MAAM,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,GAAG,CAAC;AAC5D,MAAI,OAAO,MAAM,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;AACpE,MAAI,OAAO,MAAM,IAAI,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5D,MAAI,OAAO,MAAM,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,GAAG,GAAG,IAAI,CAAC;AACpE,MAAI,OAAO,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,GAAG,IAAI,GAAG,CAAC;AACpE,MAAI,OAAO,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAE5E,MAAI,IAAI,KAAK,CAAC;AACd,MAAI,IAAI,KAAK,CAAC;AACd,MAAI,IAAI,KAAK,CAAC;AAEd,SAAO,KAAK,KAAK,KAAK,MAAM,MAAM,CAAC,GAAG,KAAK,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,KAAK,MAAM,MAAM,CAAC,GAAG,KAAK,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;AACrH;AAEA,IAAI,QAAqB,OAAO,OAAO;AAAA,EACrC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,IAAI,MAAM,KAAK,KAAK;AACpB,IAAI,WAAwB,WAAY;AACtC,WAASC,UAAS,OAAO;AACvB,oBAAgB,MAAMA,SAAQ;AAE9B,oBAAgB,MAAM,iBAAiB,CAAC;AAExC,oBAAgB,MAAM,oBAAoB,CAAC;AAE3C,oBAAgB,MAAM,eAAe,CAAC;AAEtC,oBAAgB,MAAM,cAAc,KAAK;AAEzC,oBAAgB,MAAM,gBAAgB,CAAC;AAEvC,oBAAgB,MAAM,kBAAkB,CAAC;AAEzC,oBAAgB,MAAM,cAAc,KAAK;AAEzC,oBAAgB,MAAM,aAAa,IAAI;AAEvC,oBAAgB,MAAM,cAAc,EAAE;AAEtC,oBAAgB,MAAM,eAAe,GAAI;AAEzC,oBAAgB,MAAM,eAAe,GAAK;AAE1C,oBAAgB,MAAM,eAAe,EAAE;AAEvC,oBAAgB,MAAM,eAAe,GAAG;AAExC,oBAAgB,MAAM,SAAS,CAAC;AAEhC,WAAO,OAAO,MAAM,KAAK;AAAA,EAC3B;AAEA,eAAaA,WAAU,CAAC;AAAA,IACtB,KAAK;AAAA,IACL,OAAO,SAAS,kBAAkB,aAAa;AAC7C,UAAI,gBAAgB,KAAK,OAAO,KAAK,KAAK,cAAc,KAAK,eAAe,KAAK;AACjF,WAAK,gBAAgB,cAAc,gBAAgB;AACnD,WAAK,cAAc;AACnB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ;AACtB,WAAK,gBAAgB;AACrB,WAAK,cAAc;AACnB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,OAAO,aAAa,OAAO;AACzC,UAAI,cAAc,KAAK,iBAAiB,KAAK,iBAAiB,GAAG;AAC/D,aAAK,eAAe,KAAK,MAAM,KAAK,OAAO,IAAI,KAAK,KAAK,IAAI;AAAA,MAC/D;AAEA,UAAI,KAAK,cAAc,KAAK,gBAAgB,cAAc,KAAK,eAAe;AAC5E,YAAI,CAAC,KAAK,YAAY;AACpB,eAAK,aAAa;AAClB,eAAK,iBAAiB;AACtB,cAAI,gBAAgB,KAAK,OAAO,KAAK,KAAK,cAAc,KAAK,eAAe,KAAK;AACjF,eAAK,mBAAmB,cAAc,gBAAgB;AAAA,QACxD,WAAW,KAAK,cAAc,cAAc,KAAK,kBAAkB;AACjE,eAAK,aAAa;AAClB,eAAK,aAAa;AAClB,eAAK;AAEL,cAAI,KAAK,eAAe,KAAK,cAAc;AACzC,iBAAK,eAAe;AACpB,gBAAI,KAAK,UAAW,MAAK,kBAAkB,WAAW;AAAA,UACxD;AAAA,QACF;AAAA,MACF;AAEA,UAAI,KAAK,YAAY;AACnB,aAAK,kBAAkB,QAAQ,KAAK;AACpC,aAAK,iBAAiB,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,cAAc,CAAC;AAElE,YAAI,KAAK,kBAAkB,GAAG;AAC5B,eAAK,aAAa;AAClB,eAAK,iBAAiB;AAAA,QACxB;AAAA,MACF;AAEA,aAAO,KAAK;AAAA,IACd;AAAA,EACF,CAAC,CAAC;AAEF,SAAOA;AACT,EAAE;AAEF,SAAS,cAAcL,OAAM;AAC3B,MAAI,OAAOA,UAAS,UAAU;AAC5B,IAAAA,QAAO,KAAK,IAAIA,KAAI;AAAA,EACtB,WAAW,OAAOA,UAAS,UAAU;AACnC,QAAI,SAASA;AACb,IAAAA,QAAO;AAEP,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,MAAAA,SAAQA,SAAQ,IAAI,MAAM,OAAO,WAAW,CAAC,IAAI,OAAO;AAAA,IAC1D;AAAA,EACF;AAEA,MAAIA,UAAS,GAAG;AACd,IAAAA,QAAO;AAAA,EACT;AAEA,SAAOA;AACT;AAEA,SAAS,UAAUA,OAAM;AACvB,MAAI,QAAQ,cAAcA,KAAI;AAC9B,SAAO,WAAY;AACjB,QAAI,SAAS,QAAQ,QAAQ;AAC7B,YAAQ;AACR,WAAO,SAAS;AAAA,EAClB;AACF;AAEA,IAAI,YAAY,SAASM,WAAU,OAAO;AACxC,MAAI,QAAQ;AAEZ,kBAAgB,MAAMA,UAAS;AAE/B,kBAAgB,MAAM,QAAQ,CAAC;AAE/B,kBAAgB,MAAM,QAAQ,SAAUN,OAAM;AAC5C,UAAM,OAAOA;AACb,UAAM,QAAQ,UAAUA,KAAI;AAAA,EAC9B,CAAC;AAED,kBAAgB,MAAM,SAAS,UAAU,KAAK,IAAI,CAAC;AAEnD,OAAK,KAAK,KAAK;AACjB;AACA,IAAI,aAAa,IAAI,UAAU,KAAK,OAAO,CAAC;AAK5C,IAAI,gBAAgB;AAAA,EAClB,QAAQ;AAAA,EACR,QAAQ,CAAC,GAAG,GAAG,CAAC;AAClB;AAIA,SAAS,SAAS,QAAQ,QAAQ;AAChC,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE9E,MAAI,wBAAwB,eAAe,eAAe,CAAC,GAAG,aAAa,GAAG,MAAM,GAChF,SAAS,sBAAsB,QAC/B,SAAS,sBAAsB;AAEnC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,QAAI,IAAI,IAAI,MAAM;AAClB,QAAI,IAAI,IAAI,MAAM;AAClB,QAAI,QAAQ,KAAK,KAAK,IAAI,IAAI,CAAC;AAC/B,QAAI,MAAM,MAAM;AAChB,WAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,SAAS,OAAO,CAAC;AAC/D,WAAO,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,SAAS,OAAO,CAAC;AACnE,WAAO,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,SAAS,OAAO,CAAC;AAAA,EACrD;AAEA,SAAO;AACT;AAEA,SAAS,SAAS,QAAQ,QAAQ;AAChC,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE9E,MAAI,yBAAyB,eAAe,eAAe,CAAC,GAAG,aAAa,GAAG,MAAM,GACjF,SAAS,uBAAuB,QAChC,SAAS,uBAAuB;AAEpC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,QAAI,IAAI,KAAK,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC;AACnC,QAAI,IAAI,IAAI,MAAM,IAAI,IAAI;AAC1B,QAAI,IAAI,IAAI,MAAM,IAAI,IAAI;AAC1B,QAAI,IAAI,IAAI,MAAM,IAAI,IAAI;AAC1B,QAAI,MAAM,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC;AACzC,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,WAAO,CAAC,IAAI,IAAI,SAAS,OAAO,CAAC;AACjC,WAAO,IAAI,CAAC,IAAI,IAAI,SAAS,OAAO,CAAC;AACrC,WAAO,IAAI,CAAC,IAAI,IAAI,SAAS,OAAO,CAAC;AAAA,EACvC;AAEA,SAAO;AACT;AAKA,IAAI,gBAAgB;AAAA,EAClB,QAAQ;AAAA,EACR,QAAQ,CAAC,GAAG,CAAC;AACf;AAEA,SAAS,SAAS,QAAQ,QAAQ;AAChC,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE9E,MAAI,wBAAwB,eAAe,eAAe,CAAC,GAAG,aAAa,GAAG,MAAM,GAChF,SAAS,sBAAsB,QAC/B,SAAS,sBAAsB;AAEnC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,QAAI,IAAI,SAAS,KAAK,KAAK,IAAI,MAAM,CAAC;AACtC,QAAI,QAAQ,IAAI,MAAM,IAAI;AAC1B,WAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,OAAO,CAAC;AAC1C,WAAO,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,OAAO,CAAC;AAAA,EAChD;AAEA,SAAO;AACT;AACA,SAAS,SAAS,QAAQ,QAAQ;AAChC,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE9E,MAAI,yBAAyB,eAAe,eAAe,CAAC,GAAG,aAAa,GAAG,MAAM,GACjF,SAAS,uBAAuB,QAChC,SAAS,uBAAuB;AAEpC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,QAAI,QAAQ,IAAI,MAAM,IAAI;AAC1B,WAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,SAAS,OAAO,CAAC;AAC/C,WAAO,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,SAAS,OAAO,CAAC;AAAA,EACrD;AAEA,SAAO;AACT;AAKA,IAAI,cAAc;AAAA,EAChB,OAAO;AAAA,EACP,QAAQ,CAAC,GAAG,CAAC;AACf;AACA,SAAS,OAAO,QAAQ,MAAM;AAC5B,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE9E,MAAI,oBAAoB,eAAe,eAAe,CAAC,GAAG,WAAW,GAAG,IAAI,GACxE,QAAQ,kBAAkB,OAC1B,SAAS,kBAAkB;AAE/B,MAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,CAAC;AACvD,MAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,CAAC;AAEvD,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,WAAO,CAAC,KAAK,IAAI,MAAM,IAAI,OAAO,QAAQ,OAAO,CAAC;AAClD,WAAO,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,OAAO,QAAQ,OAAO,CAAC;AAAA,EACxD;AAEA,SAAO;AACT;AACA,SAAS,OAAO,QAAQ,MAAM;AAC5B,SAAO;AACT;AAKA,SAAS,MAAM,QAAQ,KAAK;AAC1B,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE9E,MAAI,kBAAkB,eAAe,eAAe,CAAC,GAAG,UAAU,GAAG,GAAG,GACpE,QAAQ,gBAAgB,OACxB,SAAS,gBAAgB;AAE7B,MAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,CAAC;AACvD,MAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,CAAC;AACvD,MAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,CAAC;AAEvD,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,WAAO,CAAC,KAAK,IAAI,MAAM,IAAI,OAAO,QAAQ,OAAO,CAAC;AAClD,WAAO,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,OAAO,QAAQ,OAAO,CAAC;AACtD,WAAO,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,OAAO,QAAQ,OAAO,CAAC;AAAA,EACxD;AAEA,SAAO;AACT;AACA,IAAI,aAAa;AAAA,EACf,OAAO;AAAA,EACP,QAAQ,CAAC,GAAG,GAAG,CAAC;AAClB;AACA,SAAS,MAAM,QAAQ,KAAK;AAC1B,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE9E,MAAI,mBAAmB,eAAe,eAAe,CAAC,GAAG,UAAU,GAAG,GAAG,GACrE,QAAQ,iBAAiB,OACzB,SAAS,iBAAiB;AAE9B,MAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,CAAC;AACvD,MAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,CAAC;AACvD,MAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,CAAC;AAEvD,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,WAAO,CAAC,KAAK,IAAI,MAAM,IAAI,OAAO,QAAQ,OAAO,CAAC;AAClD,WAAO,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,OAAO,QAAQ,OAAO,CAAC;AACtD,WAAO,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,OAAO,QAAQ,OAAO,CAAC;AAAA,EACxD;AAEA,SAAO;AACT;AAEA,IAAI,QAAqB,OAAO,OAAO;AAAA,EACrC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;", "names": ["p", "_setPrototypeOf", "o", "matrix", "_construct", "Parent", "args", "Class", "triangle", "matrix", "p", "matrix", "p", "index", "Grad", "x", "y", "z", "seed", "simplex2", "simplex3", "perlin2", "perlin3", "FlashGen", "Generator"]}